import React, { useState, useEffect, useLayoutEffect } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  TextInput,
  ActivityIndicator,
  Pressable,
} from 'react-native';
import Swiper from 'react-native-swiper';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { variables } from '../styles/Variables';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import functions from '@react-native-firebase/functions';
import RazorpayCheckout from 'react-native-razorpay';
import { usePreferences } from '../contexts/PreferencesContext';
import HeaderBalance from '../components/HeaderBalance';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { styles, modalStyles, confirmModalStyles } from '../styles/ProfileDetailStyles';

const ProfileDetailScreen = ({ route }) => {
  const { users, index, isFavouriteUser } = route.params;
  const navigation = useNavigation();
  const [isPhoneVisible, setIsPhoneVisible] = useState(false);
  const [isFavourite, setIsFavourite] = useState(isFavouriteUser);
  const [usersIndex, setUserIndex] = useState(index);
  const [isNotInterested, setIsNotInterested] = useState(false);

  // Modal and payment states
  const [isModalVisible, setModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('plans');
  const [amount, setAmount] = useState(0);
  const [selectedPlanRange, setSelectedPlanRange] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [currentUserBalance, setCurrentUserBalance] = useState(0);

  // New state: "view" vs "recharge" context.
  const [paymentContext, setPaymentContext] = useState("view");

  // Confirmation modal state
  const [isConfirmModalVisible, setConfirmModalVisible] = useState(false);
  const [confirmationData, setConfirmationData] = useState(null);

  const userId = auth().currentUser.uid;
  const userDocRef = firestore().collection('users').doc(userId);
  const selectedUserId = users[usersIndex]?.userUid;
  const { preferences } = usePreferences();
  const enableNotification = preferences?.FCMnotification?.enableNotification;
  const fetchedPlans = preferences?.Payment?.plans;
  const minAmount = preferences?.Payment?.minAmount;
  const maxAmount = preferences?.Payment?.maxAmount;

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: () => (
        <HeaderBalance
          showRecharge={true}
          onRecharge={() => {
            setPaymentContext("recharge");
            setModalVisible(true);
            setActiveTab('plans');
          }}
        />
      ),
    });
  }, [navigation]);

  useEffect(() => {
    checkIfFavourite();
    checkIfNotInterested();
  }, [selectedUserId]);

  // Fetch current user's balance
  useEffect(() => {
    const fetchCurrentUserBalance = async () => {
      try {
        const userDoc = await userDocRef.get();
        const balance = userDoc.data()?.paidAmount || 0;
        setCurrentUserBalance(balance);
      } catch (error) {
        console.error("Error fetching user balance:", error);
      }
    };

    fetchCurrentUserBalance();
  }, []);

  useEffect(() => {
    async function updateViewedProfile() {
      try {
        if (selectedUserId) {
          await userDocRef.update({
            viewedProfileIds: firestore.FieldValue.arrayUnion(selectedUserId),
          });
        }
      } catch (error) {
        console.error('Error updating viewedProfileIds:', error);
      }
    }

    updateViewedProfile();
  }, [selectedUserId]);

  const checkIfFavourite = async (userIndexParam = usersIndex) => {
    try {
      const userDoc = await userDocRef.get();
      const favourites = userDoc.data()?.favourites || [];
      setUserIndex(userIndexParam);
      setIsFavourite(favourites.includes(users[userIndexParam]?.userUid));
    } catch (error) {
      console.error("Error checking favourite status:", error);
    }
  };

  const checkIfNotInterested = async (userIndexParam = usersIndex) => {
    try {
      const userDoc = await userDocRef.get();
      const notInterested = userDoc.data()?.notInterested || [];
      setUserIndex(userIndexParam);
      setIsNotInterested(notInterested.includes(users[userIndexParam]?.userUid));
    } catch (error) {
      console.error("Error checking not interested status:", error);
    }
  };

  const handleNotInterested = async () => {
    try {
      const targetUserDocRef = firestore().collection('users').doc(selectedUserId);
      const targetUserDoc = await targetUserDocRef.get();
      if (!targetUserDoc.exists) return;
      setIsNotInterested(true);
      setIsFavourite(false);
      const batch = firestore().batch();

      batch.update(userDocRef, {
        notInterested: firestore.FieldValue.arrayUnion(selectedUserId),
      });
      batch.update(userDocRef, {
        favourites: firestore.FieldValue.arrayRemove(selectedUserId),
      });
      batch.update(userDocRef, {
        likedByOthers: firestore.FieldValue.arrayRemove(userId),
      });

      await batch.commit();

    } catch (error) {
      setIsNotInterested(false);
      console.error("Error updating not interested status:", error);
    }
  };

  const sendFavouriteNotification = async (targetUserDoc) => {
    try {
      const fcmToken = targetUserDoc.data()?.fcmToken;
      const fullName = targetUserDoc.data()?.fullName;
      const userDoc = await userDocRef.get();
      const currentUserFirstName = userDoc.data()?.fullName || 'Someone';

      if (!fcmToken) return;

      const data = {
        tokens: [fcmToken],
        title: `Hello ${fullName}!`,
        body: `Awesome news! ${currentUserFirstName} just liked your profile! 💖 Take a look now!`,
        data: {
          screen: 'LikedByOthers',
          type: 'profile_like',
        },
      };

      const sendNotification = functions().httpsCallable('sendNotification');
      await sendNotification(data);
    } catch (error) {
      console.error('Error calling Cloud Function:', error);
    }
  };

  const handleToggleFavourite = async () => {
    try {
      const targetUserDocRef = firestore().collection('users').doc(selectedUserId);
      const targetUserDoc = await targetUserDocRef.get();
      if (!targetUserDoc.exists) return;
      const newFavouriteState = !isFavourite;
      setIsFavourite(newFavouriteState);

      const batch = firestore().batch();

      if (newFavouriteState) {
        setIsNotInterested(false);
        batch.update(userDocRef, {
          favourites: firestore.FieldValue.arrayUnion(selectedUserId),
        });
        batch.update(userDocRef, {
          notInterested: firestore.FieldValue.arrayRemove(selectedUserId),
        });
        batch.update(targetUserDocRef, {
          likedByOthers: firestore.FieldValue.arrayUnion(userId),
        });

        if (enableNotification) {
          sendFavouriteNotification(targetUserDoc);
        }
      } else {
        batch.update(userDocRef, {
          favourites: firestore.FieldValue.arrayRemove(selectedUserId),
        });
        batch.update(targetUserDocRef, {
          likedByOthers: firestore.FieldValue.arrayRemove(userId),
        });
      }

      await batch.commit();

    } catch (error) {
      setIsFavourite(!isFavourite);
      console.error("Error updating likedByOthers:", error);
    }
  };

  const updatePayment = async () => {

    if (paymentContext === "recharge") return;

    const userDoc = await userDocRef.get();
    const userNumberViewed = userDoc.data()?.numberViewedUserIds || [];
    const isAlreadyViewed = userNumberViewed.includes(users[usersIndex]?.userUid);

    if (isAlreadyViewed) {
      setIsPhoneVisible(true);
      return;
    }

    let paidAmount = userDoc.data()?.paidAmount || 0;
    if (paidAmount >= minAmount) {
      // Show elegant confirmation modal before revealing phone number
      setConfirmationData({
        paidAmount,
        minAmount,
        remainingBalance: paidAmount - minAmount,
        onConfirm: async () => {
          setConfirmModalVisible(false);
          setModalVisible(false);
          console.log("enter onConfirm");
          setIsPhoneVisible(true);
          const updatedBalance = Number(paidAmount - minAmount);
          const viewedUserUid = users[usersIndex]?.userUid;
          let updatePayload = {
            paidAmount: updatedBalance,
          };
          if (viewedUserUid !== undefined) {
            updatePayload.numberViewedUserIds = firestore.FieldValue.arrayUnion(viewedUserUid);
          }
          await userDocRef.update(updatePayload);
        },
        onCancel: () => {
          setConfirmModalVisible(false);
        }
      });
      setConfirmModalVisible(true);
    } else {
      setModalVisible(true);
      setActiveTab('plans');
    }
  };

  const handleViewPhoneNumberRequest = async () => {
    updatePayment();
  };

  // Helper to compute activePlan object based on updatedBalance.
  const getActivePlan = (updatedBalance) => {
    let planDays = 0;
    if (fetchedPlans) {
      for (const key in fetchedPlans) {
        if (fetchedPlans.hasOwnProperty(key)) {
          const [minRange, maxRange] = fetchedPlans[key]
            .split('-')
            .map(val => Number(val));
          if (updatedBalance >= minRange && updatedBalance <= maxRange) {
            planDays = Number(key);
            break;
          }
        }
      }
    }
    return planDays > 0
      ? {
        planDays: planDays,
        planAmount: updatedBalance,
        purchaseDate: firestore.FieldValue.serverTimestamp(),
      }
      : null;
  };

  // Payment submission: differentiate behavior by paymentContext.
  const handlePayment = async () => {
    const viewedUserUid = users[usersIndex]?.userUid;
    if (amount <= 0) {
      setErrorMessage("💰 Please enter a valid amount to proceed with payment");
      return;
    }
    if (!amount || !viewedUserUid) return;

    if (amount > maxAmount) {
      setErrorMessage(`💸 Maximum amount allowed is ₹${maxAmount}. Please enter a smaller amount.`);
      return;
    }

    // Check if current balance + entered amount meets minimum requirement
    const totalAfterPayment = currentUserBalance + amount;
    if (totalAfterPayment < minAmount) {
      const remainingAmount = minAmount - currentUserBalance;
      setErrorMessage(`⚠️ You need ₹${remainingAmount} more to reach the minimum ₹${minAmount} required for viewing contacts.`);
      return;
    }

    try {
      setIsLoading(true);
      setModalVisible(false);

      // functions().useEmulator('********', 5001);
      const createOrder = functions().httpsCallable('createRazorpayOrder');
      const userDoc = await userDocRef.get();
      const currrentUserId = userDoc.data()?.userId || '';
      const orderResponse = await createOrder({ amount: amount, userId: currrentUserId });
      const { id } = orderResponse.data;

      const options = {
        description: 'Malayali Match Payments',
        image: 'https://your-logo-url.com/logo.png',
        currency: 'INR',
        key: 'rzp_test_PZS9x4kZtZKBGS',
        amount: parseInt(amount) * 100,
        order_id: id,
        name: 'Malayali Match',
        prefill: {
          email: auth().currentUser.email,
          name: auth().currentUser.displayName || ''
        },
        theme: { color: variables.themeBGColor },
        method: {
          card: true,
          netbanking: true,
          wallet: false,
          upi: true,
          emi: false,
          paylater: false
        }
      };

      RazorpayCheckout.open(options)
        .then(async (data) => {
          // functions().useEmulator('********', 5001);
          setModalVisible(false);
          const verifySignature = functions().httpsCallable('verifyRazorpaySignature');
          const verifyResponse = await verifySignature({
            razorpay_payment_id: data.razorpay_payment_id,
            razorpay_order_id: data.razorpay_order_id,
            razorpay_signature: data.razorpay_signature,
          });
          if (!verifyResponse.data.valid) {
            Alert.alert("Payment service is not available now", "Please try after some time");
            setIsLoading(false);
            return;
          }
          // Retrieve the current balance.
          let paidAmount = userDoc.data()?.paidAmount || 0;
          let updatedBalance = 0;

          if (paymentContext === "view") {
            updatedBalance = (Number(paidAmount) + Number(amount));

            let updatePayload = {
              paidAmount: updatedBalance,
              activePlan: updatedBalance < minAmount ? null : getActivePlan(updatedBalance)
            };

            if (viewedUserUid !== undefined && updatedBalance >= minAmount) {
              setIsPhoneVisible(true);
              updatePayload.numberViewedUserIds = firestore.FieldValue.arrayUnion(viewedUserUid);
              updatePayload.paidAmount = updatedBalance - minAmount;
            }

            await userDocRef.update(updatePayload);
          } else {
            updatedBalance = Number(paidAmount) + Number(amount);
            let updatePayload = {
              paidAmount: updatedBalance,
              activePlan: updatedBalance < minAmount ? null : getActivePlan(updatedBalance)
            };
            await userDocRef.update(updatePayload);
            setPaymentContext("view");
          }

          // Close modal and reset form after successful payment
          setModalVisible(false);
          setAmount('');
          setErrorMessage('');
          setIsLoading(false);

          // Fetch updated balance from Firestore to ensure accuracy
          const updatedUserDoc = await userDocRef.get();
          const newBalance = updatedUserDoc.data()?.paidAmount || 0;
          setCurrentUserBalance(newBalance);
        })
        .catch(() => {
          Alert.alert("Payment service is not available now", "Please try after some time");
          setIsLoading(false);
        });
    } catch (error) {
      Alert.alert("Payment service is not available now, Please try payment after some time");
      setIsLoading(false);
    }
  };

  // Updated handleImagePress: filter out null image URLs.
  const handleImagePress = (item) => {
    const images = [
      item.profileImage1,
      item.profileImage2,
      item.profileImage3,
    ].filter(url => url);
    if (images.length === 0) {
      return;
    }
    navigation.navigate('ImageSwiperScreen', { images });
  };

  // Modified renderCarouselItem that accepts the slide's index.
  const renderCarouselItem = (item, slideIndex) => (
    <ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>
      <View style={styles.container}>
        <TouchableOpacity onPress={() => handleImagePress(item)}>
          <Image
            source={
              item.profileImage1
                ? { uri: item.profileImage1 }
                : require('../assets/splash.png')
            }
            style={styles.profileImage}
          />
        </TouchableOpacity>
        <View style={styles.overlayContainer}>
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,1)']} // Reversed colors
            start={{ x: 1, y: 0 }} // Start from bottom
            end={{ x: 1, y: 1 }} // End at top
            style={styles.gradient}
          >
            <Text style={styles.userName}>{`${item.fullName}`}</Text>
            <Text style={styles.userDetail}>{`User ID: ${item.userId}`}</Text>
          </LinearGradient>
        </View>
        <View style={styles.interestButtonsContainer}>
          <TouchableOpacity style={[styles.favButton, isFavourite && styles.interestButtonActive]}
            onPress={handleToggleFavourite}
          >
            <FontAwesome name="heart" size={15} color={isFavourite ? 'red' : '#fff'} />
            <Text style={[styles.interestButtonText, isFavourite && styles.interestButtonActive]}>Interested</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.interestButton, isNotInterested && styles.interestButtonActive]}
            onPress={handleNotInterested}
            disabled={isNotInterested}
          >
            <Text style={[styles.clearSearchButtonText, isNotInterested && styles.interestButtonActive]}>✕</Text>
            <Text style={[styles.interestButtonText, isNotInterested && styles.interestButtonActive]}>Not Interested</Text>
          </TouchableOpacity>
        </View>

      </View>
      {/* About Me Section - Always show this section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="person" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>About Me</Text>
        </View>
        <Text style={styles.infoText}>{item.aboutMe || "Not updated"}</Text>
      </View>

      {/* Basic Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="info" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Basic Details</Text>
        </View>
        <Text style={styles.infoText}>Age: {item.age || "Not updated"}</Text>
        <Text style={styles.infoText}>Date of Birth: {item.dateOfBirth || "Not updated"}</Text>
        <Text style={styles.infoText}>Gender: {item.gender || "Not updated"}</Text>
        <Text style={styles.infoText}>Height: {item.height ? `${item.height} cm` : "Not updated"}</Text>
        <Text style={styles.infoText}>Weight: {item.weight ? `${item.weight} kg` : "Not updated"}</Text>
        <Text style={styles.infoText}>Marital Status: {item.maritalStatus || "Not updated"}</Text>
        <Text style={styles.infoText}>Mother Tongue: {item.motherTongue || "Not updated"}</Text>
        <Text style={styles.infoText}>Physical Status: {item.physicalStatus || "Not updated"}</Text>
        <Text style={styles.infoText}>Star: {item.star || "Not updated"}</Text>
      </View>

      {/* Religious Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialCommunityIcons name="church" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Religious Details</Text>
        </View>
        <Text style={styles.infoText}>Religion: {
          (item.religion === 'Other' && item.customReligion) ?
            item.customReligion :
            (item.religion || "Not updated")
        }</Text>
        <Text style={styles.infoText}>Caste: {
          (item.caste === 'Other' && item.customCaste) ?
            item.customCaste :
            (item.caste || "Not updated")
        }</Text>
      </View>

      {/* Professional Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="work" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Professional Details</Text>
        </View>
        <Text style={styles.infoText}>Education: {
          (item.highestEducation === 'Other' && item.customEducation) ?
            item.customEducation :
            (item.highestEducation || item.education || "Not updated")
        }</Text>
        <Text style={styles.infoText}>Occupation: {
          (item.occupation === 'Other' && item.customOccupation) ?
            item.customOccupation :
            (item.occupation || "Not updated")
        }</Text>
        <Text style={styles.infoText}>Salary: {item.salary || item.annualIncome || "Not updated"}</Text>
        <Text style={styles.infoText}>Employed In: {
          (item.employedIn === 'Other' && item.customEmployedIn) ?
            item.customEmployedIn :
            (item.employedIn || "Not updated")
        }</Text>
      </View>

      {/* Location Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="location-on" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Location</Text>
        </View>
        <Text style={styles.infoText}>Country: {item.country || 'INDIA'}</Text>
        <Text style={styles.infoText}>State: {item.state || 'KERALA'}</Text>
        <Text style={styles.infoText}>District: {item.district || "Not updated"}</Text>
        <Text style={styles.infoText}>City: {item.city || "Not updated"}</Text>
      </View>

      {/* Habits & Lifestyle Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialCommunityIcons name="food-fork-drink" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Habits & Lifestyle</Text>
        </View>
        <Text style={styles.infoText}>Eating Habits: {item.eatingHabits || "Not updated"}</Text>
        <Text style={styles.infoText}>Drinking Habits: {item.drinkingHabits || "Not updated"}</Text>
        <Text style={styles.infoText}>Smoking Habits: {item.smokingHabits || "Not updated"}</Text>
      </View>

      {/* Contact Details Section */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="contact-phone" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Contact Details</Text>
        </View>
        {usersIndex === slideIndex && isPhoneVisible ? (
          <Text style={styles.phoneInfoText}>Phone: {item.phoneNumber || "Not updated"}</Text>
        ) : (
          <TouchableOpacity onPress={handleViewPhoneNumberRequest} style={styles.viewPhoneButton}>
            <MaterialIcons name="phone" size={16} color="#007bff" />
            <Text style={styles.viewPhoneText}>View Phone Number</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Family Details Section - Always show */}
      <View style={styles.section}>
        <View style={styles.sectionTitleContainer}>
          <MaterialIcons name="family-restroom" size={22} color={variables.themeBGColor} />
          <Text style={styles.sectionTitle}>Family Details</Text>
        </View>
        <Text style={styles.infoText}>Father: {item.father || "Not updated"}</Text>
        <Text style={styles.infoText}>Mother: {item.mother || "Not updated"}</Text>
        <Text style={styles.infoText}>Siblings: {item.siblings || "Not updated"}</Text>
      </View>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <Swiper
        index={usersIndex}
        loop={false}
        showsPagination={false}
        dotColor="#ccc"
        activeDotColor={variables.themeBGColor}
        onIndexChanged={(newIndex) => {
          checkIfFavourite(newIndex);
          checkIfNotInterested(newIndex);
          setIsPhoneVisible(false);
        }}
      >
        {users.map((user, index) => (
          <View key={user.userId} style={{ flex: 1 }}>
            {renderCarouselItem(user, index)}
          </View>
        ))}
      </Swiper>

      {/* Modal Popup for Payment and Plans */}
      <Modal visible={isModalVisible} animationType="slide" transparent={true}>
        <View style={modalStyles.modalBackground}>
          <View style={modalStyles.modalContainer}>
            <TouchableOpacity onPress={() => {
              setModalVisible(false)
              setPaymentContext("view")
            }} style={modalStyles.closeButton}>
              <Text style={modalStyles.closeButtonText}>✕</Text>
            </TouchableOpacity>
            <View style={modalStyles.tabHeader}>
              <TouchableOpacity
                onPress={() => setActiveTab('plans')}
                style={[
                  modalStyles.tabButton,
                  activeTab === 'plans' && modalStyles.activeTab,
                ]}
                activeOpacity={0.8}
              >
                <Text style={[
                  modalStyles.tabButtonText,
                  activeTab === 'plans' && modalStyles.activeTabText,
                ]}>Plans</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => setActiveTab('payment')}
                style={[
                  modalStyles.tabButton,
                  activeTab === 'payment' && modalStyles.activeTab,
                ]}
                activeOpacity={0.8}
              >
                <Text style={[
                  modalStyles.tabButtonText,
                  activeTab === 'payment' && modalStyles.activeTabText,
                ]}>Payment</Text>
              </TouchableOpacity>
            </View>
            <View style={modalStyles.tabContent}>
              {activeTab === 'plans' ? (
                <View>
                  <Text style={modalStyles.infoText}>Choose a Plan:</Text>
                  <View style={modalStyles.plansContainer}>
                    {fetchedPlans ? (
                      Object.keys(fetchedPlans).map((key) => {
                        const rangeStr = fetchedPlans[key]; // e.g., "25-500"
                        const [minVal, maxVal] = rangeStr.split('-').map(val => Number(val));
                        return (
                          <Pressable
                            key={key}
                            onPress={() => {
                              setSelectedPlanRange({ min: minVal, max: maxVal, planDays: Number(key) });
                              setActiveTab('payment');
                            }}
                            style={({ pressed, hovered }) => [
                              modalStyles.planCard,
                              (pressed || hovered) && modalStyles.planCardActive,
                            ]}
                          >
                            <View>
                              <Text style={modalStyles.planTitle}>{key} Days Plan</Text>
                              <Text style={modalStyles.planSubtitle}>Access to premium features</Text>
                            </View>
                            <View style={modalStyles.planPriceContainer}>
                              <Text style={modalStyles.planPrice}>₹{rangeStr}</Text>
                              <Text style={modalStyles.planPriceLabel}>Price Range</Text>
                            </View>
                          </Pressable>
                        );
                      })
                    ) : (
                      <View style={{
                        alignItems: 'center',
                        paddingVertical: 40,
                      }}>
                        <MaterialIcons name="hourglass-empty" size={32} color="#6c757d" />
                        <Text style={{
                          fontSize: 16,
                          color: '#6c757d',
                          marginTop: 12,
                          fontWeight: '500',
                        }}>Loading plans...</Text>
                      </View>
                    )}
                  </View>
                  {/* Helpful Information Messages */}
                  <View style={modalStyles.infoSection}>
                    <View style={modalStyles.infoCard}>
                      <MaterialIcons name="info" size={20} color="#007bff" style={{marginRight: 8}} />
                      <Text style={modalStyles.infoCardText}>
                        💡 To view a contact, you need minimum ₹{minAmount}
                      </Text>
                    </View>
                  </View>
                </View>
              ) : (
                <View>
                  {/* Helpful Information Messages for Payment Tab */}
                  <View style={modalStyles.infoSection}>
                    <View style={modalStyles.infoCard}>
                      <Text style={modalStyles.infoCardText}>
                        💰 Enter amount between ₹{minAmount} - ₹{maxAmount} to activate your plan
                      </Text>
                    </View>

                    <View style={modalStyles.infoCard}>
                      <Text style={modalStyles.infoCardText}>
                        👁️ Minimum ₹{minAmount} required to view one contact
                      </Text>
                    </View>
                  </View>

                  {selectedPlanRange ? (
                    <Text style={modalStyles.infoText}>
                      Please enter your plan amount for {selectedPlanRange.planDays} days plan.
                    </Text>
                  ) : (
                    <Text style={modalStyles.infoText}>Enter your preferred amount:</Text>
                  )}
                  <TextInput
                    style={modalStyles.input}
                    placeholder="Enter amount"
                    keyboardType="numeric"
                    value={Number(amount)}
                    onChangeText={text => {
                      const numericValue = Number(text);
                      if (numericValue > maxAmount) {
                        setErrorMessage(`💸 Maximum amount allowed is ₹${maxAmount}. Please enter a smaller amount.`);
                      } else if (numericValue < 0) {
                        setErrorMessage("💰 Please enter a valid positive amount.");
                      } else if (numericValue > 0) {
                        // Check if current balance + entered amount meets minimum requirement
                        const totalAfterPayment = currentUserBalance + numericValue;
                        if (totalAfterPayment < minAmount) {
                          const remainingAmount = minAmount - currentUserBalance;
                          setErrorMessage(`⚠️ You need ₹${remainingAmount} more to reach the minimum ₹${minAmount} required for viewing contacts.`);
                        } else {
                          setErrorMessage("");
                        }
                      } else {
                        setErrorMessage("");
                      }
                      setAmount(numericValue);
                    }}
                  />
                  {errorMessage ? (
                    <Text style={{
                      color: "#dc3545",
                      marginTop: -15,
                      marginBottom: 20,
                      fontSize: 13,
                      fontWeight: '500',
                      backgroundColor: 'rgba(220, 53, 69, 0.1)',
                      paddingVertical: 8,
                      paddingHorizontal: 12,
                      borderRadius: 8,
                      textAlign: 'center',
                    }}>{errorMessage}</Text>
                  ) : null}
                  <TouchableOpacity
                    style={modalStyles.submitButton}
                    onPress={handlePayment}
                    activeOpacity={0.8}
                  >
                    <MaterialIcons name="payment" size={18} color="#fff" style={{ marginRight: 8 }} />
                    <Text style={modalStyles.submitButtonText}>Submit Payment</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        </View>
      </Modal>

      {/* Elegant Confirmation Modal for Phone Number Viewing */}
      <Modal visible={isConfirmModalVisible} animationType="fade" transparent={true}>
        <View style={confirmModalStyles.modalBackground}>
          <View style={confirmModalStyles.modalContainer}>
            {/* Header with Icon */}
            <View style={confirmModalStyles.headerContainer}>
              <View style={confirmModalStyles.iconContainer}>
                <MaterialIcons name="phone" size={32} color="#007bff" />
              </View>
              <Text style={confirmModalStyles.title}>View Phone Number</Text>
            </View>

            {/* Content */}
            <View style={confirmModalStyles.contentContainer}>
              <Text style={confirmModalStyles.description}>
                You're about to view this user's phone number.
              </Text>

              {confirmationData && (
                <View style={confirmModalStyles.costBreakdown}>
                  <View style={confirmModalStyles.costRow}>
                    <Text style={confirmModalStyles.costLabel}>Cost:</Text>
                    <Text style={confirmModalStyles.costValue}>₹{confirmationData.minAmount}</Text>
                  </View>
                  <View style={confirmModalStyles.costRow}>
                    <Text style={confirmModalStyles.costLabel}>Current Balance:</Text>
                    <Text style={confirmModalStyles.costValue}>₹{confirmationData.paidAmount}</Text>
                  </View>
                  <View style={[confirmModalStyles.costRow, confirmModalStyles.remainingRow]}>
                    <Text style={confirmModalStyles.remainingLabel}>Remaining Balance:</Text>
                    <Text style={confirmModalStyles.remainingValue}>₹{confirmationData.remainingBalance}</Text>
                  </View>
                </View>
              )}

              <Text style={confirmModalStyles.confirmText}>
                Do you want to continue?
              </Text>
            </View>

            {/* Action Buttons */}
            <View style={confirmModalStyles.buttonContainer}>
              <TouchableOpacity
                style={confirmModalStyles.cancelButton}
                onPress={confirmationData?.onCancel}
                activeOpacity={0.8}
              >
                <Text style={confirmModalStyles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={confirmModalStyles.confirmButton}
                onPress={confirmationData?.onConfirm}
                activeOpacity={0.8}
              >
                <MaterialIcons name="check" size={18} color="#fff" style={{ marginRight: 5 }} />
                <Text style={confirmModalStyles.confirmButtonText}>Continue</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {isLoading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={variables.themeBGColor} />
        </View>
      )}
    </View>
  );
};



export default ProfileDetailScreen;