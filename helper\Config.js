import {variables} from '../styles/Variables';
import { Ionicons } from "@expo/vector-icons";

const config = {
    globalScreenOptions : {
        headerStyle: {
            backgroundColor: '#ffffff',
            borderBottomWidth: 0,
            elevation: 0,
            shadowOpacity: 0,
            shadowOffset: { height: 0, width: 0 },
            shadowRadius: 0,
        },
        headerTitleStyle: {
            color: '#1c1c1e',
            fontSize: 18,
            fontWeight: '600',
            letterSpacing: 0.3,
        },
        headerTintColor: '#007AFF',
        headerBackTitleVisible: false,
        headerShadowVisible: false,
//        headerRight: () => ( <Ionicons name="heart-circle-sharp" size={50} color={variables.white} /> )
    },

  }
  
  export {config}