import React, { useState, useEffect, useCallback, Fragment } from 'react';
import { View, Text, TouchableOpacity, Alert, Dimensions, FlatList, BackHandler } from 'react-native';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import DropDownPicker from 'react-native-dropdown-picker';
import Icon from 'react-native-vector-icons/Ionicons'; // Import Ionicons for the arrow icon
import styles from '../styles/EditPreferencesStyles';
import formValues from '../helper/FormValues';
import { useUserInteractions } from '../contexts/UserInteractionsContext';
import useScreenFocus from '../helper/useScreenFocus';

const { width } = Dimensions.get('window');

const EditPreferences = ({ navigation, route }) => {
    const [isSaving, setIsSaving] = useState(false);
    const [preferences, setPreferences] = useState({
        age: [25, 30],
        height: [120, 180],
        salary: [],
        education: [],
        employedIn: [],
        religion: [],
        caste: [],
        star: [],
        district: [],
        maritalStatus: [],
    });

    let { maritalStatusValues, districtValues, highestEducationValues, religionValues, employedInValues, salaryValues, starValues, casteData } = formValues;

    // Remove "Other" option from all preference dropdowns
    const removeOther = (array) => array.filter(item => item !== 'Other');

    maritalStatusValues = ['No Preference', ...removeOther(maritalStatusValues)];
    districtValues = ['No Preference', ...removeOther(districtValues)];
    highestEducationValues = ['No Preference', ...removeOther(highestEducationValues)];
    religionValues = ['No Preference', ...removeOther(religionValues)];
    employedInValues = ['No Preference', ...removeOther(employedInValues)];
    salaryValues = ['No Preference', ...removeOther(salaryValues)];
    starValues = ['No Preference', ...removeOther(starValues)];

    const { existingPreferences, refreshUserData } = useUserInteractions();

    // Create a memoized callback for refreshing data
    const handleRefresh = useCallback(() => {
        refreshUserData();
    }, [refreshUserData]);

    // Refresh data when the screen comes into focus
    useScreenFocus(handleRefresh);

    // Function to handle navigation based on authentication status
    const handleBackNavigation = useCallback(() => {
        const user = auth().currentUser;
        if (user) {
            // If user is authenticated, navigate to Home screen
            navigation.reset({
                index: 0,
                routes: [{ name: 'Home' }],
            });
            return true; // Prevent default behavior
        }
        // Default behavior (go back to login)
        return false;
    }, [navigation]);

    // Handle hardware back button press
    useEffect(() => {
        BackHandler.addEventListener('hardwareBackPress', handleBackNavigation);

        // Clean up event listener on component unmount
        return () => {
            BackHandler.removeEventListener('hardwareBackPress', handleBackNavigation);
        };
    }, [handleBackNavigation]);

    // Set up custom back button in header with arrow icon
    useEffect(() => {
        navigation.setOptions({
            headerLeft: () => (
                <TouchableOpacity
                    style={{ marginLeft: 15, padding: 5 }}
                    onPress={handleBackNavigation}
                >
                    <Icon name="arrow-back" size={24} color="#FFFFFF" />
                </TouchableOpacity>
            ),
        });
    }, [navigation, handleBackNavigation]);

    const [openDropdowns, setOpenDropdowns] = useState({});
    const transformItems = (items) => items.map((item) => ({ label: item, value: item }));

    // Create a flat array of all castes from all religions
    const allCastes = Object.values(casteData).flat();
    // Remove duplicates and "Select Caste" entries
    const uniqueCastes = [...new Set(allCastes)].filter(caste =>
        caste !== 'Select Caste' && caste !== undefined
    );
    // Add "No Preference" option
    const casteValues = ['No Preference', ...removeOther(uniqueCastes)];

    // State for caste options that will be updated based on selected religions
    const [casteOptions, setCasteOptions] = useState(transformItems(casteValues));

    const dropdownOptions = {
        maritalStatus: transformItems(maritalStatusValues),
        religion: transformItems(religionValues),
        star: transformItems(starValues),
        district: transformItems(districtValues),
        education: transformItems(highestEducationValues),
        employedIn: transformItems(employedInValues),
        salary: transformItems(salaryValues),
    };

    // Clear all preferences without alert (for internal use)
    const clearAllPreferences = useCallback(() => {
        setPreferences({
            age: [18, 50],
            height: [140, 200],
            salary: [],
            education: [],
            employedIn: [],
            religion: [],
            caste: [],
            star: [],
            district: [],
            maritalStatus: [],
        });
    }, []);

    // Clear all preferences with confirmation alert (for user action)
    const handleClearAll = () => {
        Alert.alert(
            'Clear All Preferences',
            'Are you sure you want to clear all your preferences? This action cannot be undone.',
            [
                {
                    text: 'Cancel',
                    style: 'cancel',
                },
                {
                    text: 'Clear All',
                    style: 'destructive',
                    onPress: clearAllPreferences,
                },
            ]
        );
    };

    // Update caste options when religion changes
    useEffect(() => {
        if (preferences.religion && preferences.religion.length > 0) {
            // Don't show caste dropdown if selected religions don't have castes
            const shouldShowCaste = !preferences.religion.every(rel =>
                rel === 'No Preference' ||
                rel === 'Other' ||
                rel === 'Jain' ||
                rel === 'Jewish' ||
                rel === 'Inter-Religion' ||
                rel === 'No Religion');

            if (shouldShowCaste) {
                // Handle multiple religion selections
                let allCasteOptions = [];

                preferences.religion.forEach(religion => {
                    // Skip if religion is one of the religions without castes
                    if (religion === 'No Preference' ||
                        religion === 'Other' ||
                        religion === 'Jain' ||
                        religion === 'Jewish' ||
                        religion === 'Inter-Religion' ||
                        religion === 'No Religion') {
                        return;
                    }

                    // Add castes for this religion
                    if (casteData[religion]) {
                        allCasteOptions = [...allCasteOptions, ...casteData[religion]];
                    }
                });

                // Remove duplicates and transform to dropdown format
                const uniqueCastes = [...new Set(allCasteOptions)].filter(caste =>
                    caste !== 'Select Caste' && caste !== undefined
                );
                setCasteOptions(transformItems(['No Preference', ...removeOther(uniqueCastes)]));
            } else {
                // If no religion with castes is selected, reset caste options
                setCasteOptions([]);

                // Clear any selected castes
                if (preferences.caste && preferences.caste.length > 0) {
                    setPreferences(prev => ({
                        ...prev,
                        caste: []
                    }));
                }
            }
        } else {
            // If no religion is selected, reset caste options
            setCasteOptions([]);

            // Clear any selected castes
            if (preferences.caste && preferences.caste.length > 0) {
                setPreferences(prev => ({
                    ...prev,
                    caste: []
                }));
            }
        }
    }, [preferences.religion]);

    // Auto-clear preferences when coming from registration
    useEffect(() => {
        const fromRegistration = route?.params?.fromRegistration;

        if (fromRegistration === true) {
            clearAllPreferences();
            // Clear the parameter to prevent clearing on subsequent visits
            navigation.setParams({ fromRegistration: undefined });
        }
    }, [route?.params?.fromRegistration, clearAllPreferences, navigation]);

    useEffect(() => {
        const fetchPreferences = async () => {
            const user = auth().currentUser;
            if (!user) return;

            // Don't fetch preferences if coming from registration
            const fromRegistration = route?.params?.fromRegistration;

            if (fromRegistration === true) {
                return;
            }

            try {
                if (existingPreferences) {
                    const fetchedPreferences = existingPreferences || {};

                    // Transform single values into arrays for multi-select dropdowns
                    // Also filter out "Other" values from display and trim all values
                    const transformedPreferences = {
                        ...fetchedPreferences,
                        salary: fetchedPreferences.salary ?
                            fetchedPreferences.salary.split(', ').map(item => item.trim()).filter(item => item !== 'Other' && item !== '') : [],
                        education: fetchedPreferences.education ?
                            fetchedPreferences.education.split(', ').map(item => item.trim()).filter(item => item !== 'Other' && item !== '') : [],
                        employedIn: fetchedPreferences.employedIn ?
                            fetchedPreferences.employedIn.split(', ').map(item => item.trim()).filter(item => item !== 'Other' && item !== '') : [],
                        religion: fetchedPreferences.religion ?
                            fetchedPreferences.religion.split(', ').map(item => item.trim()).filter(item => item !== 'Other' && item !== '') : [],
                        caste: fetchedPreferences.caste ?
                            fetchedPreferences.caste.split(', ').map(item => item.trim()).filter(item => item !== 'Other' && item !== '') : [],
                        star: fetchedPreferences.star ?
                            fetchedPreferences.star.split(', ').map(item => item.trim()).filter(item => item !== 'Other' && item !== '') : [],
                        district: fetchedPreferences.district ?
                            fetchedPreferences.district.split(', ').map(item => item.trim()).filter(item => item !== 'Other' && item !== '') : [],
                        maritalStatus: fetchedPreferences.maritalStatus ?
                            fetchedPreferences.maritalStatus.split(', ').map(item => item.trim()).filter(item => item !== 'Other' && item !== '') : [],
                    };

                    setPreferences((prev) => ({
                        ...prev,
                        ...transformedPreferences,
                    }));
                }
            } catch (error) {
                console.error('Error fetching preferences:', error);
            }
        };

        fetchPreferences();
    }, [existingPreferences, route?.params?.fromRegistration]);

    // Pre-populate preferences with default values during registration flow
    useEffect(() => {
        const prePopulateDefaultValues = async () => {
            const user = auth().currentUser;
            const fromRegistration = route?.params?.fromRegistration;

            // Only pre-populate if coming from registration
            if (!user || !fromRegistration) return;

            try {
                // Get current user data to apply default filters
                const userDoc = await firestore().collection('users').doc(user.uid).get();
                if (!userDoc.exists) return;

                const userData = userDoc.data();
                console.log("Pre-populating preferences with user data:", userData);

                // Create default preferences object based on user's profile
                const defaultPreferences = {
                    age: [18, 60], // Default age range
                    height: [100, 300], // Default height range
                    salary: [],
                    education: [],
                    employedIn: [],
                    religion: [],
                    caste: [],
                    star: [],
                    district: [],
                    maritalStatus: [],
                };

                // Apply default filters similar to HomeScreen logic
                // Religion filter (if not "Other" or "No Religion")
                if (userData.religion &&
                    userData.religion !== 'Other' &&
                    userData.religion !== 'No Religion') {
                    defaultPreferences.religion = [userData.religion];
                    console.log(`Pre-populated religion: ${userData.religion}`);
                }

                // District filter (if available)
                if (userData.district && userData.district.trim() !== '') {
                    defaultPreferences.district = [userData.district];
                    console.log(`Pre-populated district: ${userData.district}`);
                }

                // Marital status filter (if available)
                if (userData.maritalStatus && userData.maritalStatus.trim() !== '') {
                    defaultPreferences.maritalStatus = [userData.maritalStatus];
                    console.log(`Pre-populated marital status: ${userData.maritalStatus}`);
                }

                // Set the pre-populated preferences
                setPreferences(defaultPreferences);
                console.log("Default preferences applied:", defaultPreferences);

            } catch (error) {
                console.error('Error pre-populating default preferences:', error);
            }
        };

        prePopulateDefaultValues();
    }, [route?.params?.fromRegistration]);

    const handleSave = async () => {
        const user = auth().currentUser;
        if (!user) return;

        // Prevent multiple saves
        if (isSaving) return;

        setIsSaving(true);

        try {
            // Save preferences as they are, without automatically adding "Other"
            // Trim all values to remove any extra spaces and filter out empty values
            const preferencesToSave = {
                ...preferences,
                salary: preferences.salary.map(item => item.trim()).filter(item => item !== '').join(', '),
                education: preferences.education.map(item => item.trim()).filter(item => item !== '').join(', '),
                employedIn: preferences.employedIn.map(item => item.trim()).filter(item => item !== '').join(', '),
                religion: preferences.religion.map(item => item.trim()).filter(item => item !== '').join(', '),
                caste: preferences.caste.map(item => item.trim()).filter(item => item !== '').join(', '),
                star: preferences.star.map(item => item.trim()).filter(item => item !== '').join(', '),
                district: preferences.district.map(item => item.trim()).filter(item => item !== '').join(', '),
                maritalStatus: preferences.maritalStatus.map(item => item.trim()).filter(item => item !== '').join(', '),
            };

            console.log('Saving preferences:', preferencesToSave);

            await firestore().collection('users').doc(user.uid).set(
                {
                    preferences: preferencesToSave,
                    lastUpdated: firestore.FieldValue.serverTimestamp()
                },
                { merge: true }
            );

            // Refresh user data to ensure context is updated
            await refreshUserData();

            // Navigate to home screen with refresh parameter
            navigation.reset({
                index: 0,
                routes: [{ name: 'Home', params: { refreshUsers: true } }],
            });
        } catch (error) {
            console.error('Error updating preferences:', error);
            Alert.alert("Info", "We're experiencing a temporary issue with Update preferences. Please try again later.");
        } finally {
            setIsSaving(false);
        }
    };

    // Remove a selected value from the corresponding key
    const removeSelectedValue = (key, value) => {
        setPreferences((prev) => ({
            ...prev,
            [key]: prev[key].filter((item) => item !== value),
        }));
    };



    const renderItem = () => (
        <View>
            {/* Age Range Selector */}
            <Text style={styles.label}>
                Preferred Age: {preferences.age[0]} - {preferences.age[1]} years
            </Text>
            <MultiSlider
                values={preferences.age}
                min={18}
                max={60}
                step={1}
                onValuesChange={(val) =>
                    setPreferences((prev) => ({ ...prev, age: val }))
                }
                sliderLength={width - 40}
                snapped
                markerStyle={styles.markerStyle}
                trackStyle={styles.trackStyle}
                selectedStyle={styles.selectedStyle}
            />

            {/* Height Range Selector */}
            <Text style={styles.label}>
                Preferred Height: {preferences.height[0]} - {preferences.height[1]} cm
            </Text>
            <MultiSlider
                values={preferences.height}
                min={100}
                max={300}
                step={1}
                onValuesChange={(val) =>
                    setPreferences((prev) => ({ ...prev, height: val }))
                }
                sliderLength={width - 40}
                snapped
                markerStyle={styles.markerStyle}
                trackStyle={styles.trackStyle}
                selectedStyle={styles.selectedStyle}
            />

            {/* Dropdown Selectors */}
            {Object.keys(dropdownOptions).map((key) => {
                // Render the dropdown
                const renderDropdown = (
                    <View key={key} style={styles.pickerContainer}>
                        <Text style={styles.label}>
                            {key.replace(/([A-Z])/g, ' $1')}
                        </Text>

                        {/* Display Selected Values Outside (excluding "Other") */}
                        <View style={styles.selectedContainer}>
                            {preferences[key]?.length > 0 && (
                                preferences[key]
                                    .filter(item => item !== 'Other') // Filter out "Other" from display
                                    .map((item, index) => (
                                        <View key={index} style={styles.badge}>
                                            <Text style={styles.badgeText}>{item}</Text>
                                            <TouchableOpacity
                                                onPress={() => removeSelectedValue(key, item)}
                                                style={styles.removeButton}
                                                activeOpacity={0.7} // Better feedback when pressed
                                            >
                                                <Icon name="close" size={16} color="#FFFFFF" />
                                            </TouchableOpacity>
                                        </View>
                                    ))
                            )}
                        </View>

                        {/* Multi-Select DropDown (always empty when opened) */}
                        <DropDownPicker
                            open={openDropdowns[key]}
                            value={[]}
                            items={dropdownOptions[key] || []}
                            setOpen={(isOpen) => {
                                setOpenDropdowns({})
                                setOpenDropdowns((prev) => ({ ...prev, [key]: isOpen }))
                            }}
                            setValue={(callback) => {
                                const newValues = callback([]);

                                setPreferences((prev) => {
                                    const currentValues = prev[key] || [];
                                    let updatedValues = [];
                                    if (newValues.includes('No Preference')) {
                                        // If 'No Preference' is selected, keep only 'No Preference'
                                        updatedValues = ['No Preference'];
                                    } else {
                                        // Remove 'No Preference' if it was previously selected
                                        updatedValues = [
                                            ...new Set([
                                                ...currentValues.filter((val) => val !== 'No Preference'),
                                                ...newValues,
                                            ]),
                                        ];
                                    }
                                    return {
                                        ...prev,
                                        [key]: updatedValues,
                                    };
                                });
                                // Close the dropdown
                                setOpenDropdowns((prev) => ({
                                    ...prev,
                                    [key]: false,
                                }));
                            }}
                            multiple={true}
                            style={styles.picker}
                            dropDownContainerStyle={styles.dropDownContainer}
                            placeholder={`Select ${key}`}
                            listMode="SCROLLVIEW"
                        />
                    </View>
                );

                // If this is the religion dropdown, render it followed by the caste dropdown if applicable
                if (key === 'religion') {
                    return (
                        <Fragment key={`${key}-group`}>
                            {renderDropdown}

                            {/* Caste Dropdown - Only show if religion is selected and has castes */}
                            {preferences.religion &&
                             preferences.religion.length > 0 &&
                             !preferences.religion.every(rel =>
                                rel === 'No Preference' ||
                                rel === 'Other' ||
                                rel === 'Jain' ||
                                rel === 'Jewish' ||
                                rel === 'Inter-Religion' ||
                                rel === 'No Religion') &&
                             casteOptions.length > 0 && (
                                <View style={styles.pickerContainer}>
                                    <Text style={styles.label}>Caste</Text>

                                    {/* Display Selected Values Outside (excluding "Other") */}
                                    <View style={styles.selectedContainer}>
                                        {preferences.caste?.length > 0 && (
                                            preferences.caste
                                                .filter(item => item !== 'Other') // Filter out "Other" from display
                                                .map((item, index) => (
                                                    <View key={index} style={styles.badge}>
                                                        <Text style={styles.badgeText}>{item}</Text>
                                                        <TouchableOpacity
                                                            onPress={() => removeSelectedValue('caste', item)}
                                                            style={styles.removeButton}
                                                            activeOpacity={0.7} // Better feedback when pressed
                                                        >
                                                            <Icon name="close" size={16} color="#FFFFFF" />
                                                        </TouchableOpacity>
                                                    </View>
                                                ))
                                        )}
                                    </View>

                                    {/* Multi-Select DropDown for Caste */}
                                    <DropDownPicker
                                        open={openDropdowns.caste}
                                        value={[]}
                                        items={casteOptions}
                                        setOpen={(isOpen) => {
                                            setOpenDropdowns({})
                                            setOpenDropdowns((prev) => ({ ...prev, caste: isOpen }))
                                        }}
                                        setValue={(callback) => {
                                            const newValues = callback([]);

                                            setPreferences((prev) => {
                                                const currentValues = prev.caste || [];
                                                let updatedValues = [];
                                                if (newValues.includes('No Preference')) {
                                                    // If 'No Preference' is selected, keep only 'No Preference'
                                                    updatedValues = ['No Preference'];
                                                } else {
                                                    // Remove 'No Preference' if it was previously selected
                                                    updatedValues = [
                                                        ...new Set([
                                                            ...currentValues.filter((val) => val !== 'No Preference'),
                                                            ...newValues,
                                                        ]),
                                                    ];
                                                }
                                                return {
                                                    ...prev,
                                                    caste: updatedValues,
                                                };
                                            });
                                            // Close the dropdown
                                            setOpenDropdowns((prev) => ({
                                                ...prev,
                                                caste: false,
                                            }));
                                        }}
                                        multiple={true}
                                        style={styles.picker}
                                        dropDownContainerStyle={styles.dropDownContainer}
                                        placeholder="Select Caste"
                                        listMode="SCROLLVIEW"
                                    />
                                </View>
                            )}
                        </Fragment>
                    );
                }

                // For all other dropdowns, just render them normally
                return renderDropdown;
            })}

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    onPress={handleClearAll}
                    style={[styles.clearButton, isSaving && styles.buttonDisabled]}
                    disabled={isSaving}
                >
                    <Text style={styles.clearButtonText}>Clear All</Text>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={handleSave}
                    style={[styles.saveButton, isSaving && styles.saveButtonDisabled]}
                    disabled={isSaving}
                >
                    <Text style={styles.saveButtonText}>
                        {isSaving ? 'Saving...' : 'Save Preferences'}
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    );

    return (
        <FlatList
            showsVerticalScrollIndicator={false}
            data={[1]} // Dummy data for FlatList
            renderItem={renderItem}
            keyExtractor={(item) => item.toString()}
            contentContainerStyle={styles.container}
        />
    );
};

export default EditPreferences;
