import React, { useState } from 'react';
import {
  View,
  Image,
  Text,
  Alert,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import firestore from '@react-native-firebase/firestore';
import storage from '@react-native-firebase/storage';
import auth from '@react-native-firebase/auth';
import { properties } from '../helper/Property';
import localStyles from '../styles/ProfileStyles';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useUserBalance } from '../contexts/UserBalanceContext';

const MatrimonialProfile = ({ navigation }) => {
  const [uploadingIndex, setUploadingIndex] = useState(null); // Track which image is uploading

  // Get all user data from context
  const {
    balance,
    viewsAvailable,
    daysRemaining,
    planExpiryDate,
    isLoading: loading,
    currentUser: user,
    profileData,
    minAmount
  } = useUserBalance();

  // Local state for profile images (for upload functionality)
  const [localProfileImages, setLocalProfileImages] = useState([
    profileData.profileImage1 || null,
    profileData.profileImage2 || null,
    profileData.profileImage3 || null,
  ]);

  // Update local images when context data changes
  React.useEffect(() => {
    setLocalProfileImages([
      profileData.profileImage1 || null,
      profileData.profileImage2 || null,
      profileData.profileImage3 || null,
    ]);
  }, [profileData.profileImage1, profileData.profileImage2, profileData.profileImage3]);

  // No need for useEffect - all data comes from context now

  const uploadImage = async (index) => {
    const result = await launchImageLibrary({
      mediaType: 'photo',
      quality: 0.8,
      maxWidth: 700,
      maxHeight: 1000
    });

    if (result.didCancel) {
      console.log('User cancelled image picker');
      return;
    }

    if (result.errorCode) {
      console.error('ImagePicker Error: ', result.errorMessage);
      return;
    }

    const image = result.assets[0];
    const { uri } = image;
    if (!uri) return;

    try {
      setUploadingIndex(index); // Show loader for this specific image

      // Check if there's an existing image at this position
      const existingImageUrl = localProfileImages[index];

      // If there's an existing image, delete it from storage first
      if (existingImageUrl) {
        try {
          // Extract the file path from the URL to delete from storage
          const imageRef = storage().refFromURL(existingImageUrl);
          await imageRef.delete();
          console.log('Previous image deleted successfully');
        } catch (deleteError) {
          console.log('Error deleting previous image (might not exist):', deleteError.message);
          // Continue with upload even if delete fails (image might not exist in storage)
        }
      }

      // Upload the new image
      const fileName = `profileImage${index + 1}_${Date.now()}.jpg`;
      const storageRef = storage().ref(`profileImages/${user.uid}/${fileName}`);
      await storageRef.putFile(uri);
      const downloadURL = await storageRef.getDownloadURL();

      // Update local state
      const updatedImages = [...localProfileImages];
      updatedImages[index] = downloadURL;
      setLocalProfileImages(updatedImages);

      // Update Firestore
      const updateData = {};
      updateData[`profileImage${index + 1}`] = downloadURL;
      await firestore().collection('users').doc(user.uid).update(updateData);

      console.log('New image uploaded successfully');

    } catch (error) {
      console.error('Error uploading image:', error.message);
      Alert.alert('Upload Error', 'Failed to upload image. Please try again.');
    } finally {
      setUploadingIndex(null); // Hide loader
    }
  };

  const deleteImage = async (index) => {
    Alert.alert(
      'Delete Image',
      'Are you sure you want to delete this image?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setUploadingIndex(index); // Show loader while deleting

              // Get the image URL before deleting
              const imageUrl = localProfileImages[index];

              // Delete from Firebase Storage if image exists
              if (imageUrl) {
                try {
                  const imageRef = storage().refFromURL(imageUrl);
                  await imageRef.delete();
                  console.log('Image deleted from storage successfully');
                } catch (storageError) {
                  console.log('Error deleting from storage (might not exist):', storageError.message);
                  // Continue with Firestore update even if storage delete fails
                }
              }

              // Update the local state
              const updatedImages = [...localProfileImages];
              updatedImages[index] = null;
              setLocalProfileImages(updatedImages);

              // Update Firestore
              const updateData = {};
              updateData[`profileImage${index + 1}`] = firestore.FieldValue.delete();
              await firestore().collection('users').doc(user.uid).update(updateData);

              console.log('Image deleted successfully');

            } catch (error) {
              console.error('Error deleting image:', error.message);
              Alert.alert('Delete Error', 'Failed to delete image. Please try again.');

              // Revert the local state on error
              fetchUserProfile(user.uid);
            } finally {
              setUploadingIndex(null); // Hide loader
            }
          },
        },
      ]
    );
  };

  const handleLogout = () => {
    auth()
      .signOut()
      .then(() => navigation.replace(properties.LOGIN))
      .catch(error => Alert.alert('Error', `Logout failed: ${error.message}`));
  };

  // Function to open the image slider when a thumbnail is clicked
  const handleImagePress = (index = 0) => {
    // Filter out null images
    const images = localProfileImages.filter(img => img !== null);

    // Only navigate if there's at least one image
    if (images.length > 0) {
      // Find the correct index in the filtered array
      let initialIndex = 0;
      if (index > 0) {
        // Count non-null images before this index to determine the correct initialIndex
        const nonNullBeforeIndex = localProfileImages
          .slice(0, index)
          .filter(img => img !== null)
          .length;
        initialIndex = nonNullBeforeIndex;
      }

      navigation.navigate('ImageSwiperScreen', {
        images,
        initialIndex
      });
    }
  };

  if (loading) {
    return (
      <View style={localStyles.loadingContainer}>
        <ActivityIndicator size="large" color="transparent" />
      </View>
    );
  }

  const renderImageOrUploadButton = (index) => {
    if (uploadingIndex === index) {
      return (
        <View style={index === 0 ? localStyles.profileImage : localStyles.additionalPhotoImage}>
          <ActivityIndicator size="small" color="#333" />
        </View>
      );
    }

    if (localProfileImages[index] !== null) {
      // If image exists, show the image with edit and delete overlays
      return (
        <View style={{ width: '100%', height: '100%', position: 'relative' }}>
          <Image
            source={{ uri: localProfileImages[index] }}
            style={index === 0 ? localStyles.profileImage : localStyles.additionalPhotoImage}
          />
          <TouchableOpacity
            style={localStyles.editOverlay}
            onPress={() => uploadImage(index)}
          >
            <Icon name="pencil" size={16} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={{ ...localStyles.deleteOverlay, right: 50 }}
            onPress={() => deleteImage(index)}
          >
            <Icon name="delete" size={16} color="#fff" />
          </TouchableOpacity>
        </View>
      );
    }

    // If no image, show the upload button
    return (
      <View style={localStyles.plusContainer}>
        <Text style={localStyles.plusText}>+</Text>
        <Text style={localStyles.plusLabel}>
          {index === 0 ? 'Add Profile Image' : 'Upload Photo'}
        </Text>
      </View>
    );
  };

  // Function to render the thumbnail gallery
  const renderThumbnailGallery = () => {
    // Only show gallery if there's at least one image
    const hasImages = localProfileImages.some(img => img !== null);

    if (!hasImages) return null;

    // Count how many images we have
    const imageCount = localProfileImages.filter(img => img !== null).length;

    return (
      <View style={localStyles.thumbnailGalleryContainer}>
        <View style={localStyles.galleryHeader}>
          <Icon name="image-multiple" size={16} color="#007bff" style={{marginRight: 5}} />
          <Text style={localStyles.galleryTitle}>
            My Photos ({imageCount})
          </Text>
        </View>
        <Text style={localStyles.gallerySubtitle}>
          Tap on any photo to view in full screen
        </Text>
        <View style={localStyles.thumbnailRow}>
          {localProfileImages.map((image, index) => (
            image && (
              <TouchableOpacity
                key={index}
                style={localStyles.thumbnail}
                onPress={() => handleImagePress(index)}
                activeOpacity={0.7}
              >
                <Image
                  source={{ uri: image }}
                  style={localStyles.thumbnailImage}
                />
                <View style={localStyles.viewIconOverlay}>
                  <Icon name="eye" size={14} color="#fff" />
                </View>
              </TouchableOpacity>
            )
          ))}
        </View>
      </View>
    );
  };

  return (
    <ScrollView contentContainerStyle={localStyles.scrollContainer}>
      <View style={localStyles.header}>
        <View style={localStyles.uploadSectionTitle}>
          <Icon name="camera" size={16} color="#007bff" style={{marginRight: 5}} />
          <Text style={localStyles.uploadTitle}>Profile Photo</Text>
        </View>
        <TouchableOpacity
          onPress={() => uploadImage(0)}
          style={localStyles.additionalPhotoMain}
          disabled={uploadingIndex !== null} // Disable while any upload is in progress
          activeOpacity={0.8}
        >
          {renderImageOrUploadButton(0)}
        </TouchableOpacity>
      </View>

      <View style={localStyles.additionalPhotosContainer}>
        <TouchableOpacity
          onPress={() => uploadImage(1)}
          style={localStyles.additionalPhoto}
          disabled={uploadingIndex !== null}
          activeOpacity={0.8}
        >
          {renderImageOrUploadButton(1)}
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => uploadImage(2)}
          style={localStyles.additionalPhoto}
          disabled={uploadingIndex !== null}
          activeOpacity={0.8}
        >
          {renderImageOrUploadButton(2)}
        </TouchableOpacity>
      </View>

      <View style={localStyles.sectionLabel}>
        <Text style={localStyles.sectionLabelText}>Upload up to 3 profile photos</Text>
      </View>

      {/* Thumbnail Gallery - clearly separated */}
      <View style={localStyles.sectionDivider} />

      {renderThumbnailGallery()}

      <View style={localStyles.profileInfoCard}>
        <Text style={localStyles.name}>
          {profileData.fullName}
        </Text>
        <Text style={localStyles.subInfo}>User ID: {profileData.userId}</Text>
        <Text style={localStyles.subInfo}>Email: {profileData.email}</Text>
        <Text style={localStyles.subInfo}>Phone: {profileData.phoneNumber}</Text>

        {/* Enhanced Balance & Plan Section */}
        <View style={localStyles.balanceContainer}>
          <View style={localStyles.balanceHeader}>
            <Icon name="wallet" size={18} color="#28a745" style={{marginRight: 8}} />
            <Text style={localStyles.balanceTitle}>Account Overview</Text>
          </View>

          {/* Balance Row */}
          <View style={localStyles.balanceRow}>
            <View style={localStyles.balanceItem}>
              <Text style={localStyles.balanceAmount}>₹{balance}</Text>
              <Text style={localStyles.balanceSubtext}>Balance</Text>
            </View>
            <View style={localStyles.balanceItem}>
              <Text style={localStyles.balanceAmount}>{viewsAvailable}</Text>
              <Text style={localStyles.balanceSubtext}>Views Left</Text>
            </View>
          </View>

          {/* Plan Information */}
          {daysRemaining > 0 && (
            <View style={localStyles.planInfoContainer}>
              <View style={localStyles.planInfoHeader}>
                <Icon name="calendar-clock" size={16} color="#007bff" style={{marginRight: 6}} />
                <Text style={localStyles.planInfoTitle}>Active Plan</Text>
              </View>
              <View style={localStyles.planInfoRow}>
                <Text style={localStyles.planInfoText}>
                  <Text style={localStyles.planInfoLabel}>Expires in:</Text> {daysRemaining} day{daysRemaining !== 1 ? 's' : ''}
                </Text>
              </View>
              {planExpiryDate && (
                <Text style={localStyles.planExpiryText}>
                  Expires on: {planExpiryDate.toLocaleDateString()}
                </Text>
              )}
            </View>
          )}

          {/* No Active Plan Message */}
          {daysRemaining <= 0 && balance > 0 && (
            <View style={localStyles.noPlanContainer}>
              <Icon name="information" size={16} color="#6c757d" style={{marginRight: 6}} />
              <Text style={localStyles.noPlanText}>
                No active plan. Add more credits to activate a plan.
              </Text>
            </View>
          )}

          {/* Low Balance Warning with Recharge Button */}
          {balance < minAmount && (
            <View style={localStyles.lowBalanceContainer}>
              <View style={localStyles.lowBalanceContent}>
                <Icon name="alert-circle" size={16} color="#dc3545" style={{marginRight: 6}} />
                <Text style={localStyles.lowBalanceText}>
                  Minimum ₹{minAmount} required to view contacts
                </Text>
              </View>
              <TouchableOpacity
                style={localStyles.rechargeButton}
                onPress={() => navigation.navigate('ProfileDetailScreen', {
                  users: [profileData],
                  initialIndex: 0,
                  openPaymentModal: true
                })}
                activeOpacity={0.8}
              >
                <Icon name="plus-circle" size={16} color="#fff" style={{marginRight: 4}} />
                <Text style={localStyles.rechargeButtonText}>Recharge</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      <View style={localStyles.actionsContainer}>
        <TouchableOpacity
          style={localStyles.actionButton}
          onPress={() => navigation.navigate('EditProfile')}>
          <Icon name="account-edit" size={18} color="#007bff" />
          <Text style={localStyles.actionButtonText}>Edit Profile</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={localStyles.actionButton}
          onPress={() => navigation.navigate('EditPreferences')}>
          <Icon name="tune" size={18} color="#007bff" />
          <Text style={localStyles.actionButtonText}>Edit Preferences</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity onPress={handleLogout} style={localStyles.logoutButton}>
        <Text style={localStyles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default MatrimonialProfile;