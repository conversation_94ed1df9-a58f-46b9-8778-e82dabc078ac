import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import { variables } from '../styles/Variables';
import { usePreferences } from '../contexts/PreferencesContext';

const HeaderBalance = ({ onRecharge, showRecharge = false }) => {
    const [balance, setBalance] = useState(0);
    const [viewsAvailable, setViewsAvailable] = useState(0);
    const [daysRemaining, setDaysRemaining] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [currentUser, setCurrentUser] = useState(null);
    const { preferences } = usePreferences();
    const minAmount = preferences?.Payment?.minAmount;

    // Listen to auth state changes
    useEffect(() => {
        const unsubscribeAuth = auth().onAuthStateChanged((user) => {
            setCurrentUser(user);
            if (!user) {
                // User logged out, reset all states
                setBalance(0);
                setViewsAvailable(0);
                setDaysRemaining(0);
                setIsLoading(false);
            }
        });
        return () => unsubscribeAuth();
    }, []);

    useEffect(() => {
        if (!currentUser) {
            setIsLoading(false);
            return;
        }

        const userDocRef = firestore().collection('users').doc(currentUser.uid);
        const unsubscribe = userDocRef.onSnapshot(async (doc) => {
            if (doc?.exists) {
                const data = doc.data();
                let paidAmount = data.paidAmount || 0;
                let remainingDays = 0;

                if (data?.activePlan) {
                    console.log('Active plan data:', data.activePlan);
                    const { planDays, planAmount, purchaseDate } = data.activePlan;
                    if (purchaseDate && planDays && planAmount) {
                        const expiryDate = new Date(
                            purchaseDate?.toDate().getTime() + planDays * 24 * 60 * 60 * 1000
                        );
                        const now = new Date();
                        if (now < expiryDate && paidAmount > 0) {
                            remainingDays = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
                        } else {
                            // Plan expired: subtract planAmount from balance.
                            paidAmount -= planAmount;
                            if (paidAmount < 0) paidAmount = 0;
                        }
                    }
                }

                // Always update the balance, even if it's 0
                setBalance(paidAmount);
                setViewsAvailable(Math.floor(paidAmount / minAmount));
                setDaysRemaining(remainingDays);

                // If remaining days are 0 or less, reset paidAmount and activePlan in Firestore
                if ((remainingDays <= 0) && (data?.paidAmount <= 0 && data?.activePlan?.purchaseDate)) {
                    await userDocRef.update({
                        paidAmount: 0,
                        activePlan: {},
                    });
                }
            }
            setIsLoading(false);
        }, (error) => {
            console.error("Error listening to user document:", error);
            setIsLoading(false);
        });

        return () => unsubscribe();
    }, [currentUser, minAmount]);

    if (isLoading) {
        return (
            <View style={[headerStyles.container, { justifyContent: 'center' }]}>
                <ActivityIndicator size="small" color={variables.white} />
            </View>
        );
    }

    return (
        <View style={headerStyles.container}>
            <View style={headerStyles.infoContainer}>
                <Text style={headerStyles.valueText}>₹{balance}</Text>
                <Text style={headerStyles.labelText}>Your Balance</Text>
            </View>
            <View style={headerStyles.infoContainer}>
                <Text style={headerStyles.valueText}>{viewsAvailable}</Text>
                <Text style={headerStyles.labelText}>Views Left</Text>
            </View>
            <View style={headerStyles.infoContainer}>
                <Text style={headerStyles.valueText}>
                    {daysRemaining > 0 ? daysRemaining : ''}
                </Text>
                <Text style={headerStyles.labelText}>
                    {daysRemaining > 0 ? 'Days Remaining' : ''}
                </Text>
            </View>
            {balance < minAmount && showRecharge && (
                <TouchableOpacity onPress={onRecharge} style={headerStyles.rechargeButton}>
                    <Text style={headerStyles.rechargeText}>Recharge</Text>
                </TouchableOpacity>
            )}
        </View>
    );
};

const headerStyles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
        width: '100%',
        paddingVertical: 4,
    },
    infoContainer: {
        alignItems: 'center',
    },
    valueText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: variables.white,
        textAlign: 'center',
    },
    labelText: {
        fontSize: 10,
        color: variables.white,
        marginTop: 2,
        textAlign: 'center',
    },
    rechargeButton: {
        backgroundColor: variables.white,
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 30,
        borderColor: '#b27003',
        borderWidth: 1,
    },
    rechargeText: {
        color: variables.themeBGColor,
        fontSize: 12,
        fontWeight: 'bold',
    },
});

export default HeaderBalance;
