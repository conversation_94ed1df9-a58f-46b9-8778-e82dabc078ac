import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import { variables } from '../styles/Variables';
import { usePreferences } from '../contexts/PreferencesContext';

const HeaderBalance = ({ onRecharge, showRecharge = false }) => {
    const [balance, setBalance] = useState(0);
    const [viewsAvailable, setViewsAvailable] = useState(0);
    const [daysRemaining, setDaysRemaining] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const userId = auth().currentUser?.uid;
    const userDocRef = firestore().collection('users').doc(userId);
    const { preferences } = usePreferences();
    const minAmount = preferences?.Payment?.minAmount;

    useEffect(() => {
        const unsubscribe = userDocRef.onSnapshot(async (doc) => {
            if (doc?.exists) {
                const data = doc.data();
                let paidAmount = data.paidAmount || 0;
                let remainingDays = 0;

                if (data?.activePlan) {
                    console.log('Active plan data:', data.activePlan);
                    const { planDays, planAmount, purchaseDate } = data.activePlan;
                    if (purchaseDate && planDays && planAmount) {
                        const expiryDate = new Date(
                            purchaseDate?.toDate().getTime() + planDays * 24 * 60 * 60 * 1000
                        );
                        const now = new Date();
                        if (now < expiryDate) {
                            remainingDays = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
                        } else {
                            // Plan expired: subtract planAmount from balance.
                            paidAmount -= planAmount;
                            if (paidAmount < 0) paidAmount = 0;
                        }
                    }
                }

                // Always update the balance, even if it's 0
                setBalance(paidAmount);
                setViewsAvailable(Math.floor(paidAmount / minAmount));
                setDaysRemaining(remainingDays);

                // If remaining days are 0 or less, reset paidAmount and activePlan in Firestore
                if ((remainingDays <= 0) && (data?.paidAmount <= 0 && data?.activePlan?.purchaseDate)) {
                    await userDocRef.update({
                        paidAmount: 0,
                        activePlan: {},
                    });
                }
            }
            setIsLoading(false);
        }, (error) => {
            console.error("Error listening to user document:", error);
            setIsLoading(false);
        });

        return () => unsubscribe();
    }, [userDocRef]);

    if (isLoading) {
        return (
            <View style={[headerStyles.container, { justifyContent: 'center' }]}>
                <ActivityIndicator size="small" color={variables.white} />
            </View>
        );
    }

    return (
        <LinearGradient
            colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.1)']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={headerStyles.container}
        >
            <View style={headerStyles.infoContainer}>
                <Text style={headerStyles.valueText}>₹{balance}</Text>
                <Text style={headerStyles.labelText}>Your Balance</Text>
            </View>
            <View style={headerStyles.infoContainer}>
                <Text style={headerStyles.valueText}>{viewsAvailable}</Text>
                <Text style={headerStyles.labelText}>Views Left</Text>
            </View>
            <View style={headerStyles.infoContainer}>
                <Text style={headerStyles.valueText}>
                    {daysRemaining > 0 ? daysRemaining : ''}
                </Text>
                <Text style={headerStyles.labelText}>
                    {daysRemaining > 0 ? 'Days Remaining' : ''}
                </Text>
            </View>
            {balance < minAmount && showRecharge && (
                <LinearGradient
                    colors={['#ff6b6b', '#ee5a52']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={headerStyles.rechargeButton}
                >
                    <TouchableOpacity onPress={onRecharge} style={headerStyles.rechargeButtonInner}>
                        <Text style={headerStyles.rechargeText}>Recharge</Text>
                    </TouchableOpacity>
                </LinearGradient>
            )}
        </LinearGradient>
    );
};

const headerStyles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '95%',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 16,
        marginHorizontal: 8,
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.3)',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    infoContainer: {
        alignItems: 'center',
        flex: 1,
    },
    valueText: {
        fontSize: 18,
        fontWeight: '800',
        color: '#ffffff',
        textAlign: 'center',
        letterSpacing: 0.5,
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
    },
    labelText: {
        fontSize: 11,
        color: 'rgba(255, 255, 255, 0.9)',
        marginTop: 3,
        textAlign: 'center',
        fontWeight: '600',
        letterSpacing: 0.3,
    },
    rechargeButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        marginLeft: 12,
        shadowColor: '#ff6b6b',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 6,
        elevation: 6,
    },
    rechargeButtonInner: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    rechargeText: {
        color: '#ffffff',
        fontSize: 13,
        fontWeight: '700',
        letterSpacing: 0.5,
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 2,
    },
});

export default HeaderBalance;
