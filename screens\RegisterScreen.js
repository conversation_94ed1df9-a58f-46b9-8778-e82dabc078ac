import * as React from 'react';
import { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, KeyboardAvoidingView, ActivityIndicator, ScrollView, Alert, Linking } from 'react-native';
import RadioButton from '../components/RadioButton';
import TermsAndCondition from '../components/TermsAndCondition';
import { properties } from '../helper/Property';
import firestore from '@react-native-firebase/firestore';
import messaging from '@react-native-firebase/messaging';
import auth from '@react-native-firebase/auth';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import styles from '../styles/RegisterStyles';
import { variables } from '../styles/Variables';
import formValues from '../helper/FormValues';
import { usePreferences } from '../contexts/PreferencesContext';

const RegisterScreen = ({ navigation }) => {
  const PROP = [
    { key: 'male', text: 'Male' },
    { key: 'female', text: 'Female' }
  ];

  // State for form fields
  const [formData, setFormData] = useState({
    fullName: '',
    gender: '',
    email: '',
    phoneNumber: '',
    password: '',
    confirmPassword: '',
    dob: null,
    height: '',
    maritalStatus: '',
    district: '',
    religion: '',
    caste: '',
    employedIn: '',
    highestEducation: '',
    occupation: '',
    salary: '',
    star: '',
    aboutMe: '',
    promoCode: '',
    motherTongue: 'Malayalam',
    // Custom fields for "Other" options
    customReligion: '',
    customCaste: '',
    customEducation: '',
    customEmployedIn: '',
    customOccupation: ''
  });

  // Error states
  const [errors, setErrors] = useState({
    fullName: '',
    gender: '',
    email: '',
    phoneNumber: '',
    password: '',
    confirmPassword: '',
    dob: '',
    height: '',
    maritalStatus: '',
    district: '',
    religion: '',
    caste: '',
    employedIn: '',
    highestEducation: '',
    occupation: '',
    salary: '',
    star: '',
    aboutMe: '',
    // Custom fields errors
    customReligion: '',
    customCaste: '',
    customEducation: '',
    customEmployedIn: '',
    customOccupation: ''
  });

  // Form steps
  const [currentStep, setCurrentStep] = useState(1);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [loading, setLoading] = useState(false)
  const { preferences } = usePreferences();
  const registrationOfferEnabled = preferences?.Offer?.registrationOfferEnabled;
  const registrationOfferAmout = preferences?.Offer?.registrationOfferAmout;
  const terms = preferences?.Offer?.registrationOfferAmout;

  let { maritalStatusValues, districtValues, highestEducationValues, occupationValues, religionValues, employedInValues, salaryValues, starValues, casteData } = formValues;

  maritalStatusValues = ['Select Marital Status', ...maritalStatusValues],
    districtValues = ['Select District', ...districtValues],
    highestEducationValues = ['Select Education', ...highestEducationValues],
    occupationValues = ["Select Occupation", ...occupationValues],
    religionValues = ['Select Religion', ...religionValues],
    employedInValues = ['Select Employment', ...employedInValues],
    salaryValues = ["Select Salary", ...salaryValues],
    starValues = ["Select Star", ...starValues]

  // Helper functions
  const transformToOptions = (optionsArray) => {
    return optionsArray.map((option) => ({
      label: option,
      value: option.includes('Select') ? '' : option,
    }));
  };

  const calculateAge = (dateOfBirth) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const handleChange = (name, value) => {
    // Helper function to check if a string contains numbers
    const containsNumbers = (text) => /\d/.test(text);

    // Clear error when field changes
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Validate height field
    if (name === 'height') {
      // Check if value contains non-numeric characters
      if (value && !/^\d*$/.test(value)) {
        setErrors(prev => ({ ...prev, height: 'Height should only contain numbers' }));
      }
      // Check if value exceeds 3 digits
      else if (value && value.length > 3) {
        setErrors(prev => ({ ...prev, height: 'Height should not exceed 3 digits' }));
        // Truncate to 3 digits
        value = value.substring(0, 3);
      }
    }

    // Real-time validation for text-only fields (excluding phone number)
    const textOnlyFields = ['fullName', 'aboutMe', 'customEducation'];
    if (textOnlyFields.includes(name)) {
      if (containsNumbers(value)) {
        setErrors(prev => ({
          ...prev,
          [name]: name === 'customEducation'
            ? 'Education details should not contain numbers'
            : `${name.charAt(0).toUpperCase() + name.slice(1)} should not contain numbers`
        }));
      }
    }

    // Real-time password validation
    if (name === 'password' && value) {
      if (value.length < 6) {
        setErrors(prev => ({ ...prev, password: 'Password must be at least 6 characters long.' }));
      }
    }

    // Real-time confirm password validation
    if (name === 'confirmPassword' && value && formData.password) {
      if (value !== formData.password) {
        setErrors(prev => ({ ...prev, confirmPassword: 'Passwords do not match.' }));
      } else {
        // Clear error when passwords match
        setErrors(prev => ({ ...prev, confirmPassword: '' }));
      }
    }

    // Also validate when password field changes and confirm password exists
    if (name === 'password' && value && formData.confirmPassword) {
      if (value !== formData.confirmPassword) {
        setErrors(prev => ({ ...prev, confirmPassword: 'Passwords do not match.' }));
      } else {
        // Clear error when passwords match
        setErrors(prev => ({ ...prev, confirmPassword: '' }));
      }
    }

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateStep = (step) => {
    const newErrors = {};
    let isValid = true;

    if (step === 1) {
      if (!formData.fullName) {
        newErrors.fullName = properties.FULL_NAME_ERROR;
        isValid = false;
      } else if (/\d/.test(formData.fullName)) {
        newErrors.fullName = 'Full name should not contain numbers.';
        isValid = false;
      }
      if (!formData.gender) {
        newErrors.gender = 'Please select your gender.';
        isValid = false;
      }
      if (!formData.email) {
        newErrors.email = 'Email is required.';
        isValid = false;
      } else if (!/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w\w+)+$/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address.';
        isValid = false;
      }
      if (!formData.phoneNumber || !/^[6-9]\d{9}$/.test(formData.phoneNumber)) {
        newErrors.phoneNumber = 'Please enter a valid phone number.';
        isValid = false;
      }
      if (!formData.password) {
        newErrors.password = 'Password is required.';
        isValid = false;
      } else if (formData.password.length < 6) {
        newErrors.password = 'Password must be at least 6 characters long.';
        isValid = false;
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match.';
        isValid = false;
      }
      if (!formData.dob) {
        newErrors.dob = properties.DOB_ERROR;
        isValid = false;
      } else if (calculateAge(formData.dob) < 18) {
        newErrors.dob = 'You must be at least 18 years old to register.';
        isValid = false;
      }
    } else if (step === 2) {
      if (!formData.maritalStatus) {
        newErrors.maritalStatus = 'Marital status is required.';
        isValid = false;
      }
      if (!formData.district) {
        newErrors.district = 'District is required.';
        isValid = false;
      }
      if (!formData.employedIn) {
        newErrors.employedIn = 'Employment status is required.';
        isValid = false;
      }
      if (!formData.highestEducation) {
        newErrors.highestEducation = 'Education Detail is required.';
        isValid = false;
      }

      // Validate custom education for any education selection
      if (formData.highestEducation && !formData.customEducation) {
        newErrors.customEducation = 'Please provide additional education details.';
        isValid = false;
      }

      // Validate that custom education doesn't contain numbers
      if (formData.customEducation && /\d/.test(formData.customEducation)) {
        newErrors.customEducation = 'Education details should not contain numbers.';
        isValid = false;
      }

      // Validate custom employedIn if "Other" is selected
      if (formData.employedIn === 'Other' && !formData.customEmployedIn) {
        newErrors.customEmployedIn = 'Please specify your employment type.';
        isValid = false;
      }

      if (!formData.occupation) {
        newErrors.occupation = 'Occupation Detail is required.';
        isValid = false;
      }

      // Validate custom occupation if "Other" is selected
      if (formData.occupation === 'Other' && !formData.customOccupation) {
        newErrors.customOccupation = 'Please specify your occupation.';
        isValid = false;
      }

      if (!formData.salary) {
        newErrors.salary = 'Salary Detail is required.';
        isValid = false;
      }

    } else if (step === 3) {
      if (!formData.religion) {
        newErrors.religion = 'Religion is required.';
        isValid = false;
      }

      // Custom religion is optional when "Other" is selected
      // No validation required

      // Only validate Star if Religion is Hindu
      if (!formData.star && formData.religion === 'Hindu') {
        newErrors.star = 'Star is required.';
        isValid = false;
      }

      // For religions that require caste, validate that caste is selected
      // Note: When religion is "Other", caste is automatically set to "Other"
      if (!formData.caste &&
          !(formData.religion === 'Other' ||
            formData.religion === 'Jain' ||
            formData.religion === 'Jewish' ||
            formData.religion === 'Inter-Religion' ||
            formData.religion === 'No Religion')) {
        newErrors.caste = 'Caste is required.';
        isValid = false;
      }

      // Custom caste is optional when "Other" is selected for caste or religion
      // No validation required
      if (!formData.height) {
        newErrors.height = 'Height is required.';
        isValid = false;
      } else if (isNaN(formData.height)) {
        newErrors.height = 'Height should only contain numbers.';
        isValid = false;
      } else if (formData.height.toString().length > 3) {
        newErrors.height = 'Height should not exceed 3 digits.';
        isValid = false;
      }
    } else if (step === 4) {
      if (!formData.aboutMe) {
        newErrors.aboutMe = 'About me is required.';
        isValid = false;
      } else if (/\d/.test(formData.aboutMe)) {
        newErrors.aboutMe = 'About me should not contain numbers.';
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleRegistration = async () => {
    if (validateStep(4)) {
      setLoading(true);
      try {

        const fcmToken = await messaging().getToken();
        const userCredential = await auth().createUserWithEmailAndPassword(formData.email, formData.password);

        // Generate a new userId
        const userIdPrefix = "DS";
        const userIdStart = 1000;
        const usersSnapshot = await firestore().collection('users').get();
        const existingUserIds = usersSnapshot.docs
          .map(doc => doc.data().userId)
          .filter(id => id && id.startsWith(userIdPrefix))
          .map(id => parseInt(id.replace(userIdPrefix, '')));
        let newUserId = userIdStart;
        while (existingUserIds.includes(newUserId)) {
          newUserId++;
        }
        const generatedUserId = `${userIdPrefix}${newUserId}`;

        // Prepare user data with special handling for religion and caste
        const userData = {
          fullName: formData.fullName.toLowerCase(),
          gender: formData.gender,
          email: formData.email,
          phoneNumber: formData.phoneNumber,
          dateOfBirth: formData.dob?.toISOString().split('T')[0],
          age: formData.dob ? calculateAge(formData.dob) : null,
          userId: generatedUserId,
          userUid: userCredential.user.uid,
          fcmToken: fcmToken,
          likedByOthers: [],
          favourites: [],
          country: "INDIA",
          district: formData.district,
          promoCode: formData.promoCode,
          accountCreationDate: firestore.FieldValue.serverTimestamp(),
          height: Number(formData.height),
          maritalStatus: formData.maritalStatus,

          // Handle religion and caste fields
          religion: formData.religion,
          // If religion is "Other", automatically set caste to "Other" as well
          caste: formData.religion === 'Other' ? 'Other' : formData.caste,

          employedIn: formData.employedIn,
          highestEducation: formData.highestEducation,
          occupation: formData.occupation,
          salary: formData.salary,
          star: formData.star,
          aboutMe: formData.aboutMe,
          paidAmount: 0,
          motherTongue: 'Malayalam',

          // Save custom fields separately
          customReligion: formData.religion === 'Other' ? formData.customReligion : '',
          // If religion is "Other", use customCaste value regardless of caste selection
          customCaste: formData.religion === 'Other' ? formData.customCaste :
                      (formData.caste === 'Other' ? formData.customCaste : ''),
          customEducation: formData.customEducation, // Always save custom education details
          customEmployedIn: formData.employedIn === 'Other' ? formData.customEmployedIn : '',
          customOccupation: formData.occupation === 'Other' ? formData.customOccupation : '',
        };

        // Set the user data in Firestore
        await firestore().collection('users').doc(userCredential.user.uid).set(userData);

        if (registrationOfferEnabled) {
          const userDoc = await firestore().collection('users').doc(userCredential.user.uid).get();
          if (!userDoc.data().gotRegistationOffer) {
            await firestore().collection('users').doc(userCredential.user.uid).update({
              paidAmount: registrationOfferAmout,
              gotRegistationOffer: true
            });
          }
        }
        setLoading(false);
        navigation.replace('EditPreferences', { fromRegistration: true });
      } catch (error) {
        console.log('Registration Error', error.code, error.message);

        // Check for specific error codes
        if (error.code === 'auth/email-already-in-use') {
          Alert.alert("Email Already Registered", "This email address is already registered. Please use a different email or try logging in.");
        } else if (error.code === 'auth/invalid-email') {
          Alert.alert("Invalid Email", "The email address is not valid. Please check and try again.");
        } else if (error.code === 'auth/weak-password') {
          Alert.alert("Weak Password", "The password is too weak. Please choose a stronger password.");
        } else if (error.code === 'auth/network-request-failed') {
          Alert.alert("Network Error", "A network error has occurred. Please check your internet connection and try again.");
        } else {
          // Generic error for other cases
          Alert.alert("Registration Error", "We're experiencing a temporary issue with registration. Please try again later.");
        }

        setLoading(false);
      }
    }
  };

  const handleCallSupport = () => {
    const phoneNumber = '1234567890';
    Linking.openURL(`tel:${phoneNumber}`);
  };

  // Render step 1 - Basic Info
  const renderStep1 = () => (
    <>
      <TextInput
        style={errors.fullName ? styles.inputError : styles.input}
        placeholder={properties.FULL_NAME}
        value={formData.fullName}
        onChangeText={(text) => handleChange('fullName', text)}
      />
      {errors.fullName && <Text style={styles.error}>{errors.fullName}</Text>}

      <View style={styles.radioContainer}>
        <RadioButton
          PROP={PROP}
          selectedGender={formData.gender}
          onGenderChange={(gender) => handleChange('gender', gender)}
          error={errors.gender}
        />
      </View>

      <TouchableOpacity
        style={errors.dob ? styles.inputError : styles.input}
        onPress={() => setShowDatePicker(true)}
      >
        <Text>
          {formData.dob ? `Date of Birth: ${formData.dob.toLocaleDateString()}` : 'Date of Birth: Select a date*'}
        </Text>
      </TouchableOpacity>
      {errors.dob && <Text style={styles.error}>{errors.dob}</Text>}

      {showDatePicker && (
        <DateTimePicker
          value={formData.dob || new Date()}
          mode="date"
          display="spinner"
          onChange={(_, selectedDate) => {
            setShowDatePicker(false);
            if (selectedDate) {
              handleChange('dob', selectedDate);
            }
          }}
        />
      )}

      <TextInput
        style={errors.email ? styles.inputError : styles.input}
        placeholder={properties.ENTER_YOUR_EMAIL}
        value={formData.email}
        onChangeText={(text) => handleChange('email', text)}
        keyboardType="email-address"
      />
      {errors.email && <Text style={styles.error}>{errors.email}</Text>}

      <TextInput
        style={errors.phoneNumber ? styles.inputError : styles.input}
        placeholder="Enter your phone number*"
        value={formData.phoneNumber}
        onChangeText={(text) => handleChange('phoneNumber', text)}
        keyboardType="phone-pad"
      />
      {errors.phoneNumber && <Text style={styles.error}>{errors.phoneNumber}</Text>}

      <TextInput
        style={errors.password ? styles.inputError : styles.input}
        placeholder="Password*"
        value={formData.password}
        onChangeText={(text) => handleChange('password', text)}
        secureTextEntry
      />
      <Text style={styles.helperText}>
        Password must be at least 6 characters
      </Text>
      {errors.password && <Text style={styles.error}>{errors.password}</Text>}

      <TextInput
        style={errors.confirmPassword ? styles.inputError : styles.input}
        placeholder="Re-type Password*"
        value={formData.confirmPassword}
        onChangeText={(text) => handleChange('confirmPassword', text)}
        secureTextEntry
      />
      {errors.confirmPassword && <Text style={styles.error}>{errors.confirmPassword}</Text>}

      <TouchableOpacity style={styles.nextButton} onPress={nextStep}>
        <Text style={styles.buttonText}>Next</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.helpSection} onPress={handleCallSupport}>
        <FontAwesome name="phone" size={20} color="#000" />
        <Text style={styles.helpText}>Need help? Call: 1234567890</Text>
      </TouchableOpacity>
    </>
  );

  // Render step 2 - Physical & Status Info
  const renderStep2 = () => (
    <>
      <Picker
        selectedValue={formData.maritalStatus}
        style={errors.maritalStatus ? styles.inputError : styles.input}
        onValueChange={(itemValue) => handleChange('maritalStatus', itemValue)}
      >
        {transformToOptions(maritalStatusValues).map((option, index) => (
          <Picker.Item key={index} label={option.label} value={option.value} />
        ))}
      </Picker>
      {errors.maritalStatus && <Text style={styles.error}>{errors.maritalStatus}</Text>}

      <Picker
        selectedValue={formData.district}
        style={errors.district ? styles.inputError : styles.input}
        onValueChange={(itemValue) => handleChange('district', itemValue)}
      >
        {transformToOptions(districtValues).map((option, index) => (
          <Picker.Item key={index} label={option.label} value={option.value} />
        ))}
      </Picker>
      {errors.district && <Text style={styles.error}>{errors.district}</Text>}

      <Picker
        selectedValue={formData.highestEducation}
        style={errors.highestEducation ? styles.inputError : styles.input}
        onValueChange={(itemValue) => {
          handleChange('highestEducation', itemValue);

          // Don't reset custom education when changing selection
          // We want to keep it for all selections now
        }}
      >
        {transformToOptions(highestEducationValues).map((option, index) => (
          <Picker.Item key={index} label={option.label} value={option.value} />
        ))}
      </Picker>
      {errors.highestEducation && <Text style={styles.error}>{errors.highestEducation}</Text>}

      {/* Show custom education input field for any education selection */}
      {formData.highestEducation && (
        <>
          <TextInput
            style={errors.customEducation ? styles.inputError : styles.input}
            placeholder="Please provide your Highest education*"
            value={formData.customEducation}
            onChangeText={(text) => {
              // Prevent numeric input
              if (!/\d/.test(text)) {
                handleChange('customEducation', text);
              } else {
                setErrors(prev => ({
                  ...prev,
                  customEducation: 'Education details should not contain numbers'
                }));
              }
            }}
          />
          {errors.customEducation && <Text style={styles.error}>{errors.customEducation}</Text>}
        </>
      )}

      <Picker
        selectedValue={formData.employedIn}
        style={errors.employedIn ? styles.inputError : styles.input}
        onValueChange={(itemValue) => {
          handleChange('employedIn', itemValue);

          // Reset custom employedIn when changing selection
          if (itemValue !== 'Other') {
            handleChange('customEmployedIn', '');
          }
        }}
      >
        {transformToOptions(employedInValues).map((option, index) => (
          <Picker.Item key={index} label={option.label} value={option.value} />
        ))}
      </Picker>
      {errors.employedIn && <Text style={styles.error}>{errors.employedIn}</Text>}

      {/* Show custom employedIn input field when "Other" is selected */}
      {formData.employedIn === 'Other' && (
        <>
          <TextInput
            style={errors.customEmployedIn ? styles.inputError : styles.input}
            placeholder="Enter your employment type"
            value={formData.customEmployedIn}
            onChangeText={(text) => handleChange('customEmployedIn', text)}
          />
          {errors.customEmployedIn && <Text style={styles.error}>{errors.customEmployedIn}</Text>}
        </>
      )}

      <Picker
        selectedValue={formData.occupation}
        style={errors.occupation ? styles.inputError : styles.input}
        onValueChange={(itemValue) => {
          handleChange('occupation', itemValue);

          // Reset custom occupation when changing selection
          if (itemValue !== 'Other') {
            handleChange('customOccupation', '');
          }
        }}
      >
        {transformToOptions(occupationValues).map((option, index) => (
          <Picker.Item key={index} label={option.label} value={option.value} />
        ))}
      </Picker>
      {errors.occupation && <Text style={styles.error}>{errors.occupation}</Text>}

      {/* Show custom occupation input field when "Other" is selected */}
      {formData.occupation === 'Other' && (
        <>
          <TextInput
            style={errors.customOccupation ? styles.inputError : styles.input}
            placeholder="Enter your occupation"
            value={formData.customOccupation}
            onChangeText={(text) => handleChange('customOccupation', text)}
          />
          {errors.customOccupation && <Text style={styles.error}>{errors.customOccupation}</Text>}
        </>
      )}

      <Picker
        selectedValue={formData.salary}
        style={errors.salary ? styles.inputError : styles.input}
        onValueChange={(itemValue) => handleChange('salary', itemValue)}
      >
        {transformToOptions(salaryValues).map((option, index) => (
          <Picker.Item key={index} label={option.label} value={option.value} />
        ))}
      </Picker>
      {errors.salary && <Text style={styles.error}>{errors.salary}</Text>}

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.backButton} onPress={prevStep}>
          <Text style={styles.buttonText}>Back</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.nextButton} onPress={nextStep}>
          <Text style={styles.buttonText}>Next</Text>
        </TouchableOpacity>
      </View>
    </>
  );

  // Render step 3 - Religion & Caste
  const renderStep3 = () => (
    <>
      <Picker
        selectedValue={formData.religion}
        style={errors.religion ? styles.inputError : styles.input}
        onValueChange={(itemValue) => {
          handleChange('religion', itemValue);

          // If religion is "Other", automatically set caste to "Other" as well
          if (itemValue === 'Other') {
            handleChange('caste', 'Other');
          } else {
            // Otherwise, reset caste when religion changes
            handleChange('caste', '');
          }

          // Reset star when religion changes to something other than Hindu
          if (itemValue !== 'Hindu') {
            handleChange('star', '');
          }

          // Reset custom religion when changing selection
          if (itemValue !== 'Other') {
            handleChange('customReligion', '');
            handleChange('customCaste', '');
          }
        }}
      >
        {transformToOptions(religionValues).map((option, index) => (
          <Picker.Item key={index} label={option.label} value={option.value} />
        ))}
      </Picker>
      {errors.religion && <Text style={styles.error}>{errors.religion}</Text>}

      {/* Show custom religion input field when "Other" is selected */}
      {formData.religion === 'Other' && (
        <>
          <TextInput
            style={errors.customReligion ? styles.inputError : styles.input}
            placeholder="Enter your religion (optional)"
            value={formData.customReligion}
            onChangeText={(text) => handleChange('customReligion', text)}
          />
          {errors.customReligion && <Text style={styles.error}>{errors.customReligion}</Text>}

          {/* Show custom caste input field directly when religion is "Other" */}
          <TextInput
            style={errors.customCaste ? styles.inputError : styles.input}
            placeholder="Enter your caste (optional)"
            value={formData.customCaste}
            onChangeText={(text) => handleChange('customCaste', text)}
          />
          {errors.customCaste && <Text style={styles.error}>{errors.customCaste}</Text>}
        </>
      )}

      {(formData.religion === 'Jain' || formData.religion === 'Jewish' || formData.religion === 'Inter-Religion' || formData.religion === 'No Religion') ? '' :
        (formData.religion !== 'Other' ?
          <><Picker
            selectedValue={formData.caste}
            style={errors.caste ? styles.inputError : styles.input}
            enabled={!!formData.religion}
            onValueChange={(itemValue) => {
              handleChange('caste', itemValue);

              // Reset custom caste when changing selection
              if (itemValue !== 'Other') {
                handleChange('customCaste', '');
              }
            }}
          >
            {formData.religion ? (
              transformToOptions(casteData[formData.religion] || ['Select Caste']).map((option, index) => (
                <Picker.Item key={index} label={option.label} value={option.value} />
              ))
            ) : (
              <Picker.Item label="Select Religion first" value="" />
            )}
          </Picker>
            {errors.caste && <Text style={styles.error}>{errors.caste}</Text>}

            {/* Show custom caste input field when "Other" is selected */}
            {formData.caste === 'Other' && (
              <>
                <TextInput
                  style={errors.customCaste ? styles.inputError : styles.input}
                  placeholder="Enter your caste (optional)"
                  value={formData.customCaste}
                  onChangeText={(text) => handleChange('customCaste', text)}
                />
                {errors.customCaste && <Text style={styles.error}>{errors.customCaste}</Text>}
              </>
            )}
          </>
        : null)
      }

      {/* Only show Star field if Religion is Hindu */}
      {formData.religion === 'Hindu' && (
        <>
          <Picker
            selectedValue={formData.star}
            style={errors.star ? styles.inputError : styles.input}
            onValueChange={(itemValue) => handleChange('star', itemValue)}
          >
            {transformToOptions(starValues).map((option, index) => (
              <Picker.Item key={index} label={option.label} value={option.value} />
            ))}
          </Picker>
          {errors.star && <Text style={styles.error}>{errors.star}</Text>}
        </>
      )}

      <TextInput
        style={errors.height ? styles.inputError : styles.input}
        placeholder="Height* (in cm, max 3 digits)"
        value={formData.height}
        onChangeText={(text) => handleChange('height', text)}
        keyboardType="numeric"
        maxLength={3}
      />
      <Text style={styles.helperText}>Enter your height in centimeters (e.g., 175)</Text>
      {errors.height && <Text style={styles.error}>{errors.height}</Text>}

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.backButton} onPress={prevStep}>
          <Text style={styles.buttonText}>Back</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.nextButton} onPress={nextStep}>
          <Text style={styles.buttonText}>Next</Text>
        </TouchableOpacity>
      </View>
    </>
  );

  // Render step 4 - About & Submit
  const renderStep4 = () => (
    <>
      <View>
        <TextInput
          style={errors.aboutMe ? styles.inputError : styles.input}
          placeholder="Few words about you*"
          value={formData.aboutMe}
          onChangeText={(text) => {
            if (text.length <= 300) {
              handleChange('aboutMe', text);
            }
          }}
          multiline
          numberOfLines={4}
          maxLength={300}
        />
        <Text style={styles.charCount}>
          {formData.aboutMe ? formData.aboutMe.length : 0}/300 characters
        </Text>
        {errors.aboutMe && <Text style={styles.error}>{errors.aboutMe}</Text>}
      </View>

      <TextInput
        style={styles.input}
        placeholder="Enter promo code (optional)"
        value={formData.promoCode}
        onChangeText={(text) => handleChange('promoCode', text)}
      />

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.backButton} onPress={prevStep}>
          <Text style={styles.buttonText}>Back</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.submitButton} onPress={handleRegistration}>
          <Text style={styles.buttonText}>Submit</Text>
        </TouchableOpacity>
      </View>

      <TermsAndCondition />

      {loading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={variables.themeBGColor} />
        </View>
      )}
    </>
  );

  // Progress bar with step indicators
  const ProgressBar = () => {
    const steps = ['Basic Info', 'Professional', 'Personal', 'About You'];

    return (
      <View style={styles.progressContainer}>
        {/* Step indicators */}
        <View style={styles.stepIndicatorContainer}>
          {steps.map((_, index) => (
            <View key={index} style={styles.stepIndicatorWrapper}>
              <View style={[
                styles.stepIndicator,
                index + 1 <= currentStep ? styles.stepIndicatorActive : styles.stepIndicatorInactive
              ]}>
                <Text style={[
                  styles.stepIndicatorText,
                  index + 1 <= currentStep ? styles.stepIndicatorTextActive : styles.stepIndicatorTextInactive
                ]}>
                  {index + 1}
                </Text>
              </View>
              {index < steps.length - 1 && (
                <View style={[
                  styles.stepConnector,
                  index + 1 < currentStep ? styles.stepConnectorActive : styles.stepConnectorInactive
                ]} />
              )}
            </View>
          ))}
        </View>

        {/* Step text */}
        <Text style={styles.progressText}>
          Step {currentStep} of 4: {steps[currentStep - 1]}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.wrapper}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <KeyboardAvoidingView>
          <View style={styles.container}>
            <ProgressBar />

            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
            {currentStep === 3 && renderStep3()}
            {currentStep === 4 && renderStep4()}
          </View>
        </KeyboardAvoidingView>
      </ScrollView>
    </View>
  );
};

export default RegisterScreen;