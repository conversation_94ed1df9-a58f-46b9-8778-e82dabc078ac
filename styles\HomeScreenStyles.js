import { StyleSheet } from 'react-native';

// Base styles for the HomeScreen
const baseStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 10,
  },
});

// Styles for the loading and end of list components
const listStyles = StyleSheet.create({
  loadingText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
    color: '#555',
  },
  loadingMoreContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingMoreText: {
    fontSize: 14,
    color: '#555',
  },
  endOfListContainer: {
    padding: 20,
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  endOfListText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#555',
    marginBottom: 8,
  },
  endOfListSubText: {
    fontSize: 14,
    color: '#777',
    marginBottom: 16,
    textAlign: 'center',
  },
  endOfListButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  endOfListButton: {
    backgroundColor: '#2ecc71',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 6,
    marginVertical: 5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  secondaryButton: {
    backgroundColor: '#3498db',
  },
  endOfListButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  }
});

// Styles for the profile completion banner
const bannerStyles = StyleSheet.create({
  bannerContainer: {
    position: 'relative',
    width: '95%',
    backgroundColor: '#ffffff',
    padding: 20,
    paddingRight: 35,
    marginTop: 8,
    marginBottom: 16,
    flexDirection: 'column',
    borderWidth: 0,
    borderRadius: 16,
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    paddingRight: 20,
    position: 'relative',
  },
  bannerText: {
    flex: 1,
    fontSize: 15,
    color: '#1c1c1e',
    lineHeight: 22,
    fontWeight: '400',
  },
  profileLink: {
    fontSize: 15,
    color: '#007AFF',
    fontWeight: '600',
    textDecorationLine: 'none',
  },
  closeIcon: {
    padding: 4,
    position: 'absolute',
    top: -8,
    right: -8,
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(142, 142, 147, 0.12)',
  },
  closeIconText: {
    fontSize: 14,
    color: '#8e8e93',
    fontWeight: '600',
    lineHeight: 14,
    textAlign: 'center',
  },
  progressContainer: {
    height: 8,
    backgroundColor: 'rgba(142, 142, 147, 0.12)',
    borderRadius: 4,
    justifyContent: 'center',
    overflow: 'hidden',
    position: 'relative',
    marginTop: 16,
    borderWidth: 0,
  },
  progressFill: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    borderRadius: 4,
  },
  progressText: {
    textAlign: 'center',
    zIndex: 1,
    fontWeight: '600',
    color: '#1c1c1e',
    fontSize: 12,
    marginTop: 8,
    position: 'absolute',
    width: '100%',
    top: 12,
  }
});

// Styles for the offer popup
const popupStyles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContainer: {
    width: '85%',
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 123, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  popupTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  popupText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#555',
    marginBottom: 25,
    textAlign: 'center',
  },
  highlightText: {
    color: '#007bff',
    fontWeight: 'bold',
    fontSize: 18,
  },
  closeButton: {
    backgroundColor: '#007bff',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 30,
    width: '80%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#007bff',
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
});

// Export all styles
export { baseStyles, listStyles, bannerStyles, popupStyles };
