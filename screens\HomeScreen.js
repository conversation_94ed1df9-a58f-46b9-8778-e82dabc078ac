import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  Modal,
  TouchableOpacity
} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import UserCard from '../components/UserCard';
import { useUserInteractions } from '../contexts/UserInteractionsContext';
import useProfileCompletion from '../helper/useProfileCompletion';
import { baseStyles, listStyles, bannerStyles, popupStyles } from '../styles/HomeScreenStyles';

const HomeScreen = ({ navigation }) => {
  const PAGE_SIZE = 8;
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showOfferPopup, setShowOfferPopup] = useState(false);
  const [offerAmount, setOfferAmount] = useState(0);
  const { gotRegistationOffer, paidAmount } = useUserInteractions();

  const currentUser = auth().currentUser;

  // Use the profile completion hook with refresh capability
  const { profileCompletion, showProfileBanner, refreshProfileCompletion } = useProfileCompletion(currentUser);

  const [isProfileBannerVisible, setIsProfileBannerVisible] = useState(true);

  // Refresh profile completion when the screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      // Refresh profile completion when the screen is focused
      refreshProfileCompletion();

      // If we have a currentUser but no users loaded, try to fetch users
      // This helps with the notification login scenario
      if (currentUser && users.length === 0 && !loading) {
        console.log("HomeScreen: Screen focused with currentUser but no users, refetching...");
        setLoading(true);
        fetchUsers(currentUser, setLoading).then(result => {
          if (result) {
            setUsers(result.users);
            setLastDoc(result.lastDoc);
            setAllUsersLoaded(result.users.length < PAGE_SIZE);
          }
          setLoading(false);
        });
      }
    });

    return unsubscribe;
  }, [navigation, refreshProfileCompletion, currentUser, users.length, loading]);

  // Helper function to check if user only has basic preferences (age and height) or no preferences
  const checkIfOnlyBasicPreferences = (preferences) => {
    if (!preferences) return true;

    // Count the number of non-empty preference fields excluding age and height
    const significantPreferences = Object.keys(preferences).filter(key => {
      // Skip age and height
      if (key === 'age' || key === 'height') return false;

      // Check if the preference has a value
      const pref = preferences[key];
      if (!pref) return false;

      // If it's an array, check if it has meaningful values
      if (Array.isArray(pref)) {
        // Skip empty arrays
        if (pref.length === 0) return false;

        // Skip arrays that only contain "No Preference"
        if (pref.includes('No Preference')) return false;

        // Skip arrays that only contain "Other"
        if (pref.length === 1 && pref[0] === 'Other') return false;

        // Skip arrays that only contain "No Religion"
        if (key === 'religion' && pref.includes('No Religion')) return false;

        // Otherwise, this is a significant preference
        return true;
      }

      // If it's a string, check if it's not empty and not "No Preference" or "Other"
      if (typeof pref === 'string') {
        const trimmedPref = pref.trim();
        return trimmedPref !== '' &&
               (key !== 'religion' || trimmedPref !== 'No Religion');
      }

      // For other types (like objects), consider them significant
      return true;
    });

    // Log the significant preferences for debugging
    console.log("Significant preferences found:", significantPreferences);

    // Return true if there are no significant preferences
    return significantPreferences.length === 0;
  };

  // Helper function to apply default filters based on user's own profile
  const applyDefaultFilters = (preferences, userData) => {
    // Only apply default filters if the preferences object exists
    if (!preferences) return;

    console.log("User data for default filters:", JSON.stringify({
      religion: userData.religion,
      district: userData.district
    }));

    // Apply religion filter if user's religion is not "Other" or "No Religion"
    if (userData.religion &&
        userData.religion !== 'Other' &&
        userData.religion !== 'No Religion') {
      // Create or update the religion preference
      preferences.religion = [userData.religion];
      console.log(`Applied default religion filter: ${userData.religion}`);
    } else {
      console.log("Skipping religion filter - user religion is:", userData.religion);
    }

    // Apply district filter if available
    if (userData.district && userData.district.trim() !== '') {
      // Create or update the district preference
      preferences.district = [userData.district];
      console.log(`Applied default district filter: ${userData.district}`);
    } else {
      console.log("Skipping district filter - no district in user profile");
    }

    // Always apply notInterested filter
    // This is already handled in the main code, so we don't need to do anything here

    // Apply default marital status if user has one
    if (userData.maritalStatus && userData.maritalStatus.trim() !== '') {
      preferences.maritalStatus = [userData.maritalStatus];
      console.log(`Applied default marital status filter: ${userData.maritalStatus}`);
    }
  };

  const fetchUsers = async (currentUser, setLoadingState, lastDoc = null, pageSize = PAGE_SIZE) => {
    if (!currentUser) {
      return;
    }

    try {
      // Get current user data
      const userProfileDoc = await firestore().collection('users').doc(currentUser.uid).get();
      if (!userProfileDoc.exists) return;

      const userData = userProfileDoc.data();
      const oppositeGender = userData?.gender === "male" ? "female" : "male";
      const viewedProfileIds = userData?.viewedProfileIds || [];
      const userPreferences = userData?.preferences || {};
      const notInteresteds = userData?.notInterested || [];

      // Check if user has minimal or no preferences
      const hasOnlyBasicPreferences = checkIfOnlyBasicPreferences(userPreferences);

      // Log the current preferences state
      console.log("Current preferences:", JSON.stringify(userPreferences));
      console.log("User data:", JSON.stringify({
        gender: userData.gender,
        age: userData.age,
        height: userData.height,
        religion: userData.religion
      }));

      // If user has only basic preferences or no preferences, use default filters
      if (hasOnlyBasicPreferences) {
        console.log("Using default filters based on user profile");
        applyDefaultFilters(userPreferences, userData);
      }

      // Build the query with server-side filters where possible
      let query = firestore()
        .collection('users')
        .where('gender', '==', oppositeGender);

      // Track how many filters we've applied (Firestore has a limit of 10 'in' or '==' clauses per query)
      let filterCount = 1; // Start at 1 because we already used one for gender

      // Helper function to apply a filter using 'in' operator
      const applyFilter = (field, values) => {
        // Skip if no values or we've reached the filter limit
        if (!values || filterCount >= 9) return false;

        // Convert comma-separated string to array if needed and trim values
        const valuesArray = Array.isArray(values) ? values : values.split(',').map(item => item.trim());

        // Skip if "No Preference" is included in the values
        if (valuesArray.includes('No Preference')) return false;

        // Skip religion filter if "No Religion" is included
        if (field === 'religion' && valuesArray.includes('No Religion')) return false;

        // Skip filter if "Other" is included in the values (for any field except employedIn)
        if (field !== 'employedIn' && valuesArray.includes('Other')) {
          console.log(`Skipping ${field} filter because it includes "Other"`);
          return false;
        }

        // Only apply if we have values and haven't reached the limit
        if (valuesArray.length > 0 && filterCount < 9) {
          console.log(`Applying ${field} filter with values:`, valuesArray);
          query = query.where(field, 'in', valuesArray);
          filterCount++;
          return true;
        }
        return false;
      };

      // Apply religion filter if available
      if (userPreferences.religion) {
        applyFilter('religion', userPreferences.religion);
      }

      // Apply caste filter if available
      if (userPreferences.caste) {
        applyFilter('caste', userPreferences.caste);
      }

      // Apply marital status filter if available
      if (userPreferences.maritalStatus) {
        applyFilter('maritalStatus', userPreferences.maritalStatus);
      }

      // Apply district filter if available
      if (userPreferences.district) {
        applyFilter('district', userPreferences.district);
      }

      // Check for age filtering
      const hasAgeFilter = userPreferences.age && Array.isArray(userPreferences.age) && userPreferences.age.length === 2;

      // Add height filtering if available
      const hasHeightFilter = userPreferences.height && Array.isArray(userPreferences.height) && userPreferences.height.length === 2;

      // Add ordering - must consider all range filters
      // When using multiple range filters, we need to be careful with orderBy
      if (hasAgeFilter && hasHeightFilter) {
        // If we have both age and height filters, we need to choose one for orderBy
        query = query.where('age', '>=', userPreferences.age[0])
          .where('age', '<=', userPreferences.age[1])
          .orderBy('age')
          .orderBy('accountCreationDate', 'desc');

        // Apply height filter client-side instead of server-side when both filters are present
      } else if (hasAgeFilter) {
        query = query.where('age', '>=', userPreferences.age[0])
          .where('age', '<=', userPreferences.age[1])
          .orderBy('age')
          .orderBy('accountCreationDate', 'desc');
      } else if (hasHeightFilter) {
        query = query.where('height', '>=', userPreferences.height[0])
          .where('height', '<=', userPreferences.height[1])
          .orderBy('height')
          .orderBy('accountCreationDate', 'desc');
      } else {
        // No range filters
        query = query.orderBy('accountCreationDate', 'desc');
      }

      // If we have a last document, start after it for pagination
      if (lastDoc) {
        query = query.startAfter(lastDoc);
      }

      query = query.limit(pageSize);
      const snapshot = await query.get();

      // If no results, return
      if (snapshot.empty) {
        if (setLoadingState) setLoadingState(false);
        return { users: [], lastDoc: null };
      }

      // Get the last visible document for pagination
      const lastVisible = snapshot.docs[snapshot.docs.length - 1];

      let fetchedUsers = snapshot.docs.map(doc => ({ userUid: doc.id, ...doc.data() }));
      console.log(`Fetched ${fetchedUsers.length} users from Firestore`);

      // Apply client-side filters efficiently, stopping if count drops below 10
      function applyFilters(users, preferences) {
        if (!preferences || users.length === 0 || users.length <= 0) {
          return users;
        }

        console.log("Applying client-side filters to", users.length, "users");

        // Create an array of filter functions to apply
        const filterFunctions = [];

        // Apply height filter client-side if we have both age and height filters
        // (since we can only apply one range filter server-side)
        if (hasAgeFilter && hasHeightFilter) {
          filterFunctions.push(user =>
            user.height >= preferences.height[0] && user.height <= preferences.height[1]
          );
        }

        // Add remaining filters that are only applied client-side
        if (preferences.salary) {
          const salaryValues = Array.isArray(preferences.salary)
            ? preferences.salary
            : preferences.salary.split(',').map(item => item.trim());

          if (salaryValues.length > 0 && !salaryValues.includes('No Preference')) {
            filterFunctions.push(user => salaryValues.includes(user.salary));
          }
        }

        // District is already filtered server-side, no need to duplicate here

        // Caste is already filtered server-side, no need to duplicate here

        if (preferences.education) {
          const educationValues = Array.isArray(preferences.education)
            ? preferences.education
            : preferences.education.split(',').map(item => item.trim());

          // Skip if "No Preference" is included or if "Other" is included
          if (educationValues.length > 0 &&
              !educationValues.includes('No Preference') &&
              !educationValues.includes('Other')) {
            filterFunctions.push(user => educationValues.includes(user.highestEducation || user.education));
          }
        }

        if (preferences.employedIn) {
          const employedInValues = Array.isArray(preferences.employedIn)
            ? preferences.employedIn
            : preferences.employedIn.split(',').map(item => item.trim());

          // employedIn doesn't have an "Other" value
          if (employedInValues.length > 0 &&
              !employedInValues.includes('No Preference')) {
            filterFunctions.push(user => employedInValues.includes(user.employedIn));
          }
        }

        if (preferences.star) {
          const starValues = Array.isArray(preferences.star)
            ? preferences.star
            : preferences.star.split(',').map(item => item.trim());

          // Skip if "No Preference" is included or if "Other" is included
          if (starValues.length > 0 &&
              !starValues.includes('No Preference') &&
              !starValues.includes('Other')) {
            filterFunctions.push(user => starValues.includes(user.star));
          }
        }

        // Apply all filters sequentially, checking count after each
        let filteredUsers = [...users];

        // Always handle notInteresteds client-side to avoid Firestore limitations
        if (notInteresteds && notInteresteds.length > 0) {
          filteredUsers = filteredUsers.filter(user => !notInteresteds.includes(user.userUid));
          // If we already have 10 or fewer users after this filter, return early
          if (filteredUsers.length <= 0) return filteredUsers;
        }

        for (const filterFn of filterFunctions) {
          // Skip if we already have 10 or fewer users
          if (filteredUsers.length <= 0) break;

          filteredUsers = filteredUsers.filter(filterFn);
        }

        return filteredUsers;
      }

      let filteredUsers = applyFilters(fetchedUsers, userPreferences);
      console.log(`After client-side filtering: ${filteredUsers.length} users`);

      // Sort by viewed status if applicable
      if (viewedProfileIds.length > 0) {
        filteredUsers = filteredUsers.sort((a, b) => {
          const aViewed = viewedProfileIds?.includes(a.userUid);
          const bViewed = viewedProfileIds?.includes(b.userUid);
          return aViewed - bViewed;
        });
      }

      if (setLoadingState) setLoadingState(false);
      return { users: filteredUsers, lastDoc: lastVisible };
    } catch (error) {
      console.error('Error fetching users:', error);
      if (setLoadingState) setLoadingState(false);
      return { users: [], lastDoc: null };
    }
  };

  // State for pagination
  const [lastDoc, setLastDoc] = useState(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [allUsersLoaded, setAllUsersLoaded] = useState(false);

  // Memoized function to load more users when scrolling
  const loadMoreUsers = React.useCallback(async () => {
    if (isLoadingMore || allUsersLoaded || !currentUser) return;

    setIsLoadingMore(true);
    const result = await fetchUsers(currentUser, null, lastDoc);

    if (result && result.users.length > 0) {
      // Add new users, ensuring no duplicates by checking userUid
      setUsers(prevUsers => {
        const existingUserIds = new Set(prevUsers.map(user => user.userUid));
        const newUsers = result.users.filter(user => !existingUserIds.has(user.userUid));
        return [...prevUsers, ...newUsers];
      });
      setLastDoc(result.lastDoc);
      // If we got fewer new users than expected after filtering duplicates, we might be near the end
      if (result.users.length < PAGE_SIZE) {
        setAllUsersLoaded(true);
      }
    } else {
      setAllUsersLoaded(true);
    }

    setIsLoadingMore(false);
  }, [currentUser, isLoadingMore, allUsersLoaded, lastDoc, PAGE_SIZE]);

  // Initial load of users
  useEffect(() => {
    if (!currentUser) {
      setUsers([]);
      setLastDoc(null);
      setAllUsersLoaded(false);
      return;
    }

    setLoading(true);
    setAllUsersLoaded(false);

    // Initial fetch of users
    fetchUsers(currentUser, setLoading).then(result => {
      if (result) {
        setUsers(result.users);
        setLastDoc(result.lastDoc);
        setAllUsersLoaded(result.users.length < PAGE_SIZE); // If we got less than the page size, all users are loaded
      }
      setLoading(false);
    });

    if (gotRegistationOffer && paidAmount) {
      setOfferAmount(paidAmount);
      setShowOfferPopup(true);
    }
  }, [currentUser]);


  // Memoized function to get progress color based on percentage
  const getProgressColor = React.useCallback((percentage) => {
    if (percentage < 50) return '#e74c3c'; // red
    if (percentage < 75) return '#f8a75f'; // orange
    if (percentage < 85) return '#f39c12'; // light orange
    if (percentage < 95) return '#27ae60'; // green shade
    return '#2ecc71'; // bright green
  }, []);

  // Memoized function to close offer popup
  const closeOfferPopup = React.useCallback(async () => {
    setShowOfferPopup(false);
    if (currentUser) {
      await firestore().collection('users').doc(currentUser.uid).update({
        gotRegistationOffer: false,
      });
    }
  }, [currentUser]);

  // Memoized render item function to prevent unnecessary re-renders
  const renderItem = React.useCallback(({ item, index }) => (
    <UserCard
      item={item}
      users={users}
      index={index}
      screenName="ProfileDetailScreen"
    />
  ), [users]);

  // Memoized key extractor
  const keyExtractor = React.useCallback((item, index) => `${item.userUid}_${index}`, []);

  // Memoized footer component
  const ListFooterComponent = React.useCallback(() => {
    if (isLoadingMore) {
      return (
        <View style={listStyles.loadingMoreContainer}>
          <Text style={listStyles.loadingMoreText}>Loading more...</Text>
        </View>
      );
    }

    // Show message when no users are found
    if (users.length === 0 && !loading) {
      return (
        <View style={listStyles.endOfListContainer}>
          <Text style={listStyles.endOfListText}>No matches found!</Text>
          <Text style={listStyles.endOfListSubText}>
            Try adjusting your preferences to see more profiles
          </Text>
          <View style={listStyles.endOfListButtonsContainer}>
            <TouchableOpacity
              style={listStyles.endOfListButton}
              onPress={() => navigation.navigate('EditPreferences')}
            >
              <Text style={listStyles.endOfListButtonText}>Update Preferences</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[listStyles.endOfListButton, listStyles.secondaryButton]}
              onPress={() => navigation.navigate('Search')}
            >
              <Text style={listStyles.endOfListButtonText}>Try Search</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // Show message when all users have been viewed
    if (allUsersLoaded && users.length > 0) {
      return (
        <View style={listStyles.endOfListContainer}>
          <Text style={listStyles.endOfListText}>You've seen all available matches!</Text>
          <Text style={listStyles.endOfListSubText}>
            Looking for more compatible profiles?
          </Text>
          <View style={listStyles.endOfListButtonsContainer}>
            <TouchableOpacity
              style={listStyles.endOfListButton}
              onPress={() => navigation.navigate('EditPreferences')}
            >
              <Text style={listStyles.endOfListButtonText}>Update Preferences</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[listStyles.endOfListButton, listStyles.secondaryButton]}
              onPress={() => navigation.navigate('Search')}
            >
              <Text style={listStyles.endOfListButtonText}>Try Search</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    return null;
  }, [isLoadingMore, allUsersLoaded, users.length, navigation]);

  // Profile completion banner component
  const ProfileBanner = React.useMemo(() => {
    if (!showProfileBanner || !isProfileBannerVisible) return null;

    return (
      <View style={bannerStyles.bannerContainer}>
        <View style={bannerStyles.topRow}>
          <Text style={bannerStyles.bannerText}>
            Your profile is {profileCompletion}% complete.{' '}
            <Text style={bannerStyles.profileLink} onPress={() => navigation.navigate('Profile')}>
              Update Profile
            </Text>{' '}
            to unlock more matches and rewards!
          </Text>
          <TouchableOpacity onPress={() => setIsProfileBannerVisible(false)} style={bannerStyles.closeIcon}>
            <Text style={bannerStyles.closeIconText}>✕</Text>
          </TouchableOpacity>
        </View>
        <View style={bannerStyles.progressContainer}>
          <View
            style={[
              bannerStyles.progressFill,
              { width: `${profileCompletion}%`, backgroundColor: getProgressColor(profileCompletion) }
            ]}
          />
          <Text style={bannerStyles.progressText}>{profileCompletion}%</Text>
        </View>
      </View>
    );
  }, [showProfileBanner, isProfileBannerVisible, profileCompletion, navigation]);

  // Offer popup component
  const OfferPopup = React.useMemo(() => {
    if (!showOfferPopup) return null;

    return (
      <Modal transparent={true} visible={showOfferPopup} animationType="fade">
        <View style={popupStyles.modalBackground}>
          <View style={popupStyles.modalContainer}>
            <View style={popupStyles.iconContainer}>
              <MaterialIcons name="celebration" size={50} color="#007bff" />
            </View>
            <Text style={popupStyles.popupTitle}>Congratulations!</Text>
            <Text style={popupStyles.popupText}>
              Your registration is complete! You've earned <Text style={popupStyles.highlightText}>{offerAmount}</Text> reward points!
            </Text>
            <TouchableOpacity onPress={closeOfferPopup} style={popupStyles.closeButton}>
              <Text style={popupStyles.closeButtonText}>Got it!</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  }, [showOfferPopup, offerAmount]);

  return (
    <View style={baseStyles.container}>
      {ProfileBanner}
      {OfferPopup}

      {loading && users.length === 0 ? (
        <Text style={listStyles.loadingText}>Loading...</Text>
      ) : (
        <FlatList
          showsVerticalScrollIndicator={false}
          data={users}
          keyExtractor={keyExtractor}
          renderItem={renderItem}
          onEndReached={loadMoreUsers}
          onEndReachedThreshold={0.5}
          ListFooterComponent={ListFooterComponent}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={21}
          initialNumToRender={PAGE_SIZE}
        />
      )}
    </View>
  );
};

export default HomeScreen;