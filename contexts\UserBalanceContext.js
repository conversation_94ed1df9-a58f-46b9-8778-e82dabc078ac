import React, { createContext, useContext, useState, useEffect } from 'react';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import { usePreferences } from './PreferencesContext';

// Create the context
const UserBalanceContext = createContext();

// Context provider component
export const UserBalanceProvider = ({ children }) => {
  const [balance, setBalance] = useState(0);
  const [viewsAvailable, setViewsAvailable] = useState(0);
  const [daysRemaining, setDaysRemaining] = useState(0);
  const [planExpiryDate, setPlanExpiryDate] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState(null);
  const [profileData, setProfileData] = useState({});
  
  const { preferences } = usePreferences();
  const minAmount = preferences?.Payment?.minAmount || 25;

  // Listen to auth state changes
  useEffect(() => {
    const unsubscribeAuth = auth().onAuthStateChanged((user) => {
      setCurrentUser(user);
      if (!user) {
        // User logged out, reset all states
        setBalance(0);
        setViewsAvailable(0);
        setDaysRemaining(0);
        setPlanExpiryDate(null);
        setProfileData({});
        setIsLoading(false);
      }
    });
    return () => unsubscribeAuth();
  }, []);

  // Single onSnapshot listener for user data
  useEffect(() => {
    if (!currentUser) {
      setIsLoading(false);
      return;
    }

    const userDocRef = firestore().collection('users').doc(currentUser.uid);
    const unsubscribe = userDocRef.onSnapshot(async (doc) => {
      if (doc?.exists) {
        const data = doc.data();
        
        // Update profile data for ProfileScreen
        setProfileData({
          fullName: data.fullName || '',
          age: data.age || '',
          gender: data.gender || '',
          dateOfBirth: data.dateOfBirth || '',
          email: data.email || '',
          phoneNumber: data.phoneNumber || '',
          userId: data.userId || '',
          profileImage1: data.profileImage1 || null,
          profileImage2: data.profileImage2 || null,
          profileImage3: data.profileImage3 || null,
        });

        // Calculate balance and plan information
        let paidAmount = data.paidAmount || 0;
        let remainingDays = 0;
        let expiryDate = null;

        if (data?.activePlan) {
          console.log('Active plan data:', data.activePlan);
          const { planDays, planAmount, purchaseDate } = data.activePlan;
          if (purchaseDate && planDays && planAmount) {
            expiryDate = new Date(
              purchaseDate?.toDate().getTime() + planDays * 24 * 60 * 60 * 1000
            );
            const now = new Date();
            if (now < expiryDate && paidAmount > 0) {
              remainingDays = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
            } else {
              // Plan expired: subtract planAmount from balance
              paidAmount -= planAmount;
              if (paidAmount < 0) paidAmount = 0;
            }
          }
        }

        // Update balance and plan states
        setBalance(paidAmount);
        setViewsAvailable(Math.floor(paidAmount / minAmount));
        setDaysRemaining(remainingDays);
        setPlanExpiryDate(expiryDate);

        // If remaining days are 0 or less, reset paidAmount and activePlan in Firestore
        if ((remainingDays <= 0) && (data?.paidAmount <= 0 && data?.activePlan?.purchaseDate)) {
          await userDocRef.update({
            paidAmount: 0,
            activePlan: {},
          });
        }
      }
      setIsLoading(false);
    }, (error) => {
      console.error("Error listening to user document:", error);
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, [currentUser, minAmount]);

  const value = {
    balance,
    viewsAvailable,
    daysRemaining,
    planExpiryDate,
    isLoading,
    currentUser,
    profileData,
    minAmount,
  };

  return (
    <UserBalanceContext.Provider value={value}>
      {children}
    </UserBalanceContext.Provider>
  );
};

// Custom hook for consuming the context
export const useUserBalance = () => {
  const context = useContext(UserBalanceContext);
  if (!context) {
    throw new Error('useUserBalance must be used within a UserBalanceProvider');
  }
  return context;
};
