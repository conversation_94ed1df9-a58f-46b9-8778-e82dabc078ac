import { StyleSheet, Dimensions } from 'react-native';
import { variables } from '../styles/Variables';

const { width } = Dimensions.get('window');
const cardWidth = width * 0.9;

export default StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    backgroundColor: '#f8f9fa',
    paddingVertical: 15,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0, left: 0, right: 0, bottom: 0,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  header: {
    alignItems: 'center',
    marginBottom: 12,
    width: '100%',
  },
  uploadSectionTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  uploadTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50',
  },
  profileImage: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
  imagePlaceholder: {
    fontSize: 14,
    color: '#aaa',
    textAlign: 'center',
    lineHeight: 130,
  },
  profileInfoCard: {
    backgroundColor: 'transparent',
    padding: 15,
    width: cardWidth,
    marginTop: 0,
    marginBottom: 8,
    alignItems: 'center',
    paddingBottom: 15,
  },
  name: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2c3e50',
    textAlign: 'center',
    textTransform: 'uppercase',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  subInfo: {
    fontSize: 14,
    color: '#6c757d',
    textAlign: 'center',
    marginTop: 5,
    paddingHorizontal: 5,
    fontWeight: '400',
  },
  additionalPhotosContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  additionalPhoto: {
    width: 90,
    height: 70,
    borderRadius: 8,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 6,
    overflow: 'hidden',
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  additionalPhotoMain: {
    width: 110,
    height: 110,
    borderRadius: 10,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  additionalPhotoImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  plusContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#007bff',
    borderRadius: 8,
    padding: 3,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 123, 255, 0.05)',
  },
  plusText: {
    fontSize: 28,
    color: '#007bff',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 0,
  },
  plusLabel: {
    fontSize: 10,
    color: '#007bff',
    fontWeight: '500',
    textAlign: 'center',
  },
  actionsContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    width: cardWidth,
    marginTop: 10,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginVertical: 5,
    width: '100%',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007bff',
    marginLeft: 8,
  },
  logoutButton: {
    marginTop: 15,
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: variables.themeBGColor,
    borderRadius: 8,
    width: cardWidth,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: variables.themeBGColor,
  },
  logoutButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  // Styles for thumbnail gallery
  thumbnailGalleryContainer: {
    width: cardWidth,
    marginTop: 5,
    marginBottom: 15,
    paddingVertical: 12,
    backgroundColor: 'transparent',
  },
  galleryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  galleryTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50',
    textAlign: 'center',
  },
  gallerySubtitle: {
    fontSize: 12,
    color: '#6c757d',
    marginBottom: 10,
    textAlign: 'center',
  },
  thumbnailRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  thumbnail: {
    width: 60,
    height: 60,
    marginHorizontal: 6,
    borderRadius: 6,
    overflow: 'hidden',
    borderWidth: 0,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  viewIconOverlay: {
    position: 'absolute',
    bottom: 3,
    right: 3,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  editOverlay: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    backgroundColor: 'rgba(0, 123, 255, 0.8)',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  deleteOverlay: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    backgroundColor: 'rgba(220, 53, 69, 0.8)',
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  sectionLabel: {
    width: cardWidth,
    alignItems: 'center',
    marginTop: 5,
    marginBottom: 5,
  },
  sectionLabelText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  sectionDivider: {
    width: cardWidth * 0.8,
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 10,
  },
  // Balance Section Styles
  balanceContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginTop: 15,
    width: '100%',
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  balanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  balanceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
  },
  balanceAmountContainer: {
    alignItems: 'center',
  },
  balanceAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#28a745',
    marginBottom: 4,
  },
  balanceSubtext: {
    fontSize: 12,
    color: '#6c757d',
    fontStyle: 'italic',
  },
});
