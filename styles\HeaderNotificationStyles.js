import {StyleSheet} from 'react-native'
import {variables} from './Variables'

export default StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 16,
      paddingHorizontal: 8,
      paddingVertical: 8,
    },
    icon: {
      position: 'relative',
      width: 36,
      height: 36,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 122, 255, 0.1)',
      borderRadius: 18,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    badge: {
      position: 'absolute',
      top: -2,
      right: -2,
      backgroundColor: '#FF3B30',
      borderRadius: 10,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderWidth: 2,
      borderColor: '#ffffff',
      minWidth: 20,
      alignItems: 'center',
    },
    badgeText: {
      color: '#ffffff',
      fontSize: 11,
      fontWeight: '700',
      textAlign: 'center',
    },
  });
  
  