import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Platform } from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import HomeScreen from './HomeScreen';
import FavouriteScreen from './FavouriteScreen';
import SearchScreen from './SearchScreen';
import ProfileScreen from './ProfileScreen';
import LikedByOthersScreen from './LikedByOthersScreen';

import HeaderNotification from '../components/HeaderNotification';
import HeaderBalance from '../components/HeaderBalance';

const Tab = createBottomTabNavigator();

const HomeTabScreen = ({ route }) => {
  // Get the initial screen from route params
  const initialRouteName = route?.params?.screen || 'HomeTab';

  return (
    <Tab.Navigator
      initialRouteName={initialRouteName}
      screenOptions={({ route }) => ({
        tabBarIcon: ({ color, size, focused }) => {
          let iconName;
          size = focused ? 24 : 22;

          if (route.name === 'HomeTab') {
            iconName = 'home';
          } else if (route.name === 'Favourite') {
            iconName = 'heart';
          } else if (route.name === 'LikedByOthers') {
            iconName = 'users';
          } else if (route.name === 'Search') {
            iconName = 'search';
          } else if (route.name === 'Profile') {
            iconName = 'user';
          }

          return <FontAwesome name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: '#8E8E93',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopWidth: 0,
          elevation: 15,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: -3,
          },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          height: Platform.OS === 'ios' ? 85 : 65,
          paddingBottom: Platform.OS === 'ios' ? 25 : 8,
          paddingTop: 8,
          borderTopLeftRadius: 15,
          borderTopRightRadius: 15,
        },
        tabBarLabelStyle: {
          fontSize: 11,
          fontWeight: '600',
          marginTop: -2,
        },
        headerStyle: {
          backgroundColor: '#ffffff',
          borderBottomWidth: 0,
          elevation: 0,
          shadowOpacity: 0.1,
          shadowOffset: { height: 1, width: 0 },
          shadowRadius: 3,
          shadowColor: '#000000',
        },
        headerTitleStyle: {
          color: '#1c1c1e',
          fontSize: 18,
          fontWeight: '600',
          letterSpacing: 0.3,
        },
        headerTintColor: '#007AFF',
        headerShadowVisible: false,
      })}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeScreen}
        options={{
          title: 'Home',
          headerTitle: () => <HeaderBalance />,
          headerRight: () => <HeaderNotification />,
        }}
      />
      <Tab.Screen
        name="Favourite"
        component={FavouriteScreen}
        options={{ title: 'Favourite' }}
      />
      <Tab.Screen
        name="LikedByOthers"
        component={LikedByOthersScreen}
        options={{ title: 'Liked By Others' }}
      />
      <Tab.Screen
        name="Search"
        component={SearchScreen}
        options={{ title: 'Search' }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

export default HomeTabScreen;
